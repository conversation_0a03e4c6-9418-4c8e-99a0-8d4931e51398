# Chat Room API Documentation (MongoDB)

## Overview

The Chat Room API provides a streamlined solution for patient-nurse communication with secure messaging, MongoDB storage, caching, and audit trails for medical compliance and scrutiny purposes. This API is designed to work with existing patient and nurse management systems.

## Base URL
```
http://localhost:8080/api/chat
```

## Authentication

All API endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your_jwt_token>
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {
    // Response data
  }
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "field_name",
      "message": "Validation error message"
    }
  ]
}
```

## Core Endpoints

### 1. Conversations

#### 1.1 Create Conversation
**POST** `/conversations`

Creates a new conversation between a patient and nurse. Both patients and nurses can initiate conversations.

**Request Body:**
```json
{
  "title": "Medical Consultation",
  "nurseId": "nurse-uuid-from-your-api"  // Required when patient creates conversation
  // OR
  "customerId": "patient-uuid-from-your-api"  // Required when nurse creates conversation
}
```

**Response:**
```json
{
  "success": true,
  "message": "Conversation created successfully",
  "data": {
    "conversation": {
      "id": "mongodb-object-id",
      "customerId": "patient-uuid",
      "nurseId": "nurse-uuid",
      "title": "Medical Consultation",
      "status": "active",
      "createdAt": "2024-01-15T10:30:00Z",
      "createdBy": "patient"
    }
  }
}
```

**Error Responses:**

- **400 Bad Request** - Missing required participant ID:
```json
{
  "success": false,
  "message": "Nurse ID is required for patient to create conversation",
  "error": "MISSING_NURSE_ID"
}
```

- **400 Bad Request** - Invalid participants:
```json
{
  "success": false,
  "message": "Cannot create conversation with yourself",
  "error": "INVALID_PARTICIPANTS"
}
```

- **409 Conflict** - Conversation already exists:
```json
{
  "success": false,
  "message": "Active conversation already exists between these participants",
  "error": "CONVERSATION_EXISTS",
  "data": {
    "conversationId": "existing-conversation-id",
    "customerId": "patient-uuid",
    "nurseId": "nurse-uuid",
    "title": "Existing Title"
  }
}
```

#### 1.2 Get User Conversations
**GET** `/conversations`

Retrieves conversations for the authenticated user with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)

**Response:**
```json
{
  "success": true,
  "message": "Conversations retrieved successfully",
  "data": {
    "conversations": [
      {
        "_id": "mongodb-object-id",
        "customerId": "patient-uuid",
        "nurseId": "nurse-uuid",
        "title": "Medical Consultation",
        "status": "active",
        "messageCount": 15,
        "lastMessageAt": "2024-01-15T14:45:00Z",
        "createdAt": "2024-01-15T10:30:00Z",
        "updatedAt": "2024-01-15T14:45:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 1
    }
  }
}
```

#### 1.3 Get Conversation Details
**GET** `/conversations/:conversationId`

Retrieves detailed information about a specific conversation.

**Response:**
```json
{
  "success": true,
  "message": "Conversation retrieved successfully",
  "data": {
    "_id": "mongodb-object-id",
    "customerId": "patient-uuid",
    "nurseId": "nurse-uuid",
    "title": "Medical Consultation",
    "status": "active",
    "messageCount": 15,
    "lastMessageAt": "2024-01-15T14:45:00Z",
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T14:45:00Z",
    "metadata": {}
  }
}
```

#### 1.4 Mark Conversation Inactive
**PATCH** `/conversations/:conversationId/inactive`

Marks a conversation as inactive (no more messages can be sent).

**Response:**
```json
{
  "success": true,
  "message": "Conversation marked as inactive successfully"
}
```

#### 1.5 Archive Conversation
**PATCH** `/conversations/:conversationId/archive`

Archives a conversation for long-term storage.

**Response:**
```json
{
  "success": true,
  "message": "Conversation archived successfully"
}
```

#### 1.6 Get Active Conversations Count
**GET** `/conversations/active-count`

Returns the count of active conversations for the authenticated user.

**Response:**
```json
{
  "success": true,
  "message": "Active conversations count retrieved successfully",
  "data": {
    "count": 3,
    "userType": "patient"
  }
}
```

### 2. Messages

#### 2.1 Send Message
**POST** `/conversations/:conversationId/messages`

Sends a message in a conversation.

**Request Body:**
```json
{
  "message": "Hello, I have a question about my medication schedule.",
  "messageType": "text"  // Optional: text, image, file, audio, video (default: text)
}
```

**Response:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "messageId": "mongodb-object-id",
    "conversationId": "mongodb-object-id",
    "message": "Hello, I have a question about my medication schedule.",
    "messageType": "text",
    "senderId": "user-uuid",
    "senderType": "patient",
    "timestamp": "2024-01-15T14:45:00Z"
  }
}
```

#### 2.2 Get Messages
**GET** `/conversations/:conversationId/messages`

Retrieves messages from a conversation with pagination.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 50, max: 100)

**Response:**
```json
{
  "success": true,
  "message": "Messages retrieved successfully",
  "data": {
    "messages": [
      {
        "id": "uuid-string",
        "conversation_id": "uuid-string",
        "sender_id": "user-uuid",
        "sender_type": "patient",
        "message": "Hello, I have a question about my medication schedule.",
        "message_type": "text",
        "is_read": false,
        "created_at": "2024-01-15T14:45:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1
    }
  }
}
```

#### 2.3 Search Messages
**GET** `/conversations/:conversationId/messages/search`

Searches for messages within a conversation.

**Query Parameters:**
- `q` (required): Search term (minimum 2 characters)

**Response:**
```json
{
  "success": true,
  "message": "Search completed successfully",
  "data": {
    "messages": [
      {
        "id": "uuid-string",
        "conversation_id": "uuid-string",
        "sender_id": "user-uuid",
        "sender_type": "patient",
        "message": "I have a question about my medication schedule.",
        "message_type": "text",
        "created_at": "2024-01-15T14:45:00Z"
      }
    ],
    "searchTerm": "medication",
    "total": 1
  }
}
```

### 3. Analytics & Scrutiny

#### 3.1 Get Conversation Statistics
**GET** `/conversations/:conversationId/stats`

Retrieves detailed statistics for a conversation (useful for scrutiny and analysis).

**Response:**
```json
{
  "success": true,
  "message": "Conversation statistics retrieved successfully",
  "data": {
    "conversation": {
      "id": "uuid-string",
      "patient_id": "patient-uuid",
      "nurse_id": "nurse-uuid",
      "title": "Medical Consultation",
      "status": "active"
    },
    "statistics": {
      "total_messages": 15,
      "patient_messages": 8,
      "nurse_messages": 7,
      "first_message": "2024-01-15T10:30:00Z",
      "last_message": "2024-01-15T14:45:00Z",
      "avg_message_length": 45.2,
      "message_frequency": [
        {
          "hour": 10,
          "message_count": 3
        },
        {
          "hour": 14,
          "message_count": 12
        }
      ]
    }
  }
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 201 | Created |
| 400 | Bad Request - Validation error |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Access denied |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error |

## Rate Limiting

- **Limit**: 100 requests per 15 minutes per IP address
- **Headers**: Rate limit information is included in response headers
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## Validation Rules

### Conversation Creation
- `title`: Optional, max 100 characters
- `nurseId`/`customerId`: Required, valid UUID format (from your existing APIs)

### Message Sending
- `message`: Required, max 1000 characters
- `messageType`: Optional, must be one of: text, image, file

### Search
- `q`: Required, minimum 2 characters

## Caching

The API uses Redis caching for improved performance:

- **Active conversations**: 1 hour TTL
- **Chat history**: 24 hours TTL
- **User sessions**: 30 minutes TTL
- **Inactive chats**: 7 days TTL

Cache is automatically invalidated when:
- New messages are sent
- Conversations are updated
- User sessions change

## Audit Trail

All activities are logged for compliance and scrutiny:

- Conversation creation/deletion
- Message sending/editing
- User access and authentication
- IP addresses and timestamps
- Before/after values for changes

## Security Features

- JWT-based authentication
- Role-based access control (patient/nurse)
- Input validation and sanitization
- SQL injection protection
- Rate limiting
- CORS protection
- Security headers (Helmet)

## Integration with Existing APIs

This chat API is designed to work with your existing patient and nurse management systems:

1. **Patient IDs**: Use patient UUIDs from your patient management API
2. **Nurse IDs**: Use nurse UUIDs from your nurse management API
3. **User Authentication**: JWT tokens should contain user type and ID
4. **User Information**: Fetch user details from your existing APIs as needed

## WebSocket Support (Future)

For real-time messaging, WebSocket endpoints will be available:

```
ws://localhost:8080/ws?token=<jwt_token>
```

WebSocket message types:
- `join_room`: Join a conversation room
- `leave_room`: Leave a conversation room
- `send_message`: Send a real-time message
- `typing`: Typing indicator
- `read_receipt`: Message read receipt

## Testing

Use the provided test file to verify API functionality:

```bash
npm run test:api
```

Make sure to:
1. Start the server: `npm run dev`
2. Set up database and Redis
3. Update the test token in `test/api-test.js`
4. Replace placeholder UUIDs with actual IDs from your APIs
5. Run the tests

## Support

For API support and questions:
- Check the health endpoint: `GET /health`
- Review server logs for detailed error information
- Ensure all environment variables are properly configured 