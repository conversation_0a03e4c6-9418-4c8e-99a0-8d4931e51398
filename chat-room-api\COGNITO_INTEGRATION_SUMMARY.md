# Cognito Integration Summary  
chat-room-api · July 2025

This document summarizes every change required to bring the **multi-pool AWS Cognito authentication** logic from `nurserv-c-api` into **`chat-room-api`**.  
It is intended for backend engineers, DevOps, security reviewers, and anyone operating the service.

---

## 1 · Key Objectives

| Goal | Result |
|------|--------|
| Support **Patient** & **Nurse** user pools | Two JWKS clients, dynamic pool selection |
| Unify auth for **REST** _and_ **WebSocket** | Shared verification helpers in HTTP middleware & ws manager |
| Provide **Redis-backed sessions** | Prevents token reuse after logout / allows TTL refresh |
| Allow **local development** without Cognito | `USE_TEST_AUTH=true` + HMAC `JWT_TEST_SECRET` |
| Maintain backward safety | Legacy `JWT_SECRET` still accepted when explicit fallback needed |

---

## 2 · Files Added / Updated

| Path | Purpose |
|------|---------|
| `src/middleware/auth.js` | Full rewrite – multi-pool Cognito verification, Redis session check, role guards, conversation guard |
| `src/utils/websocket.js` | Added same verification logic for WS connections (`verifyAndGetUser`) |
| `src/routes/chatRoutes.js` | Enabled `authenticateUser`, granular `requirePatient`, `requirePatientOrNurse` |
| `.env.production` / `.env.development` | New Cognito, test-auth, and security variables |
| `docs/AUTHENTICATION.md` | Dev & ops reference (detailed flow) |

---

## 3 · Environment Variables

| Variable | Description |
|----------|-------------|
| `AWS_REGION` | Region of user pools |
| `CUSTOMER_COGNITO_USER_POOL_ID` / `APP_CLIENT_ID` | Patient pool |
| `NURSE_COGNITO_USER_POOL_ID` / `APP_CLIENT_ID` | Nurse pool |
| `USE_TEST_AUTH` (`true`/`false`) | Enable test tokens in dev |
| `JWT_TEST_SECRET` | HMAC key for development tokens |
| `JWT_SECRET` | Legacy fallback (only used if explicitly coded elsewhere) |
| `REDIS_HOST` … | Session cache |

All new keys have been added to both `.env.production` & `.env.development` templates.

---

## 4 · Runtime Flow

1. **Client** attaches `Authorization: Bearer <idToken>` to every REST call (or `?token=` in WS URL).  
2. `authenticateUser` middleware  
   a. If `USE_TEST_AUTH=true` ➜ verify via `JWT_TEST_SECRET`.  
   b. Else decode header to get `kid` & `iss`.  
   c. Pick matching pool (patient/nurse) and fetch signing key with `jwks-rsa` (cached, rate-limited).  
   d. Verify signature + `aud` (clientId) + `iss`.  
   e. Create `req.user = { id, type, email, username … }`.  
   f. Confirm short-lived session key exists in Redis: `session:<id>:<type>`; extend TTL 30 min.  
3. **Role Guards** (`requirePatient`, `requireNurse`, `requirePatientOrNurse`) protect routes.  
4. **validateConversationAccess** ensures only participants access a conversation.  
5. **WebSocketManager** repeats steps 2a-2d before accepting the socket.

---

## 5 · Local Development & Testing

```
NODE_ENV=development
USE_TEST_AUTH=true
JWT_TEST_SECRET=test-secret
```

Generate a token:

```bash
node test/generate-test-tokens.js --userType nurse --sub 42
```

Use this token in Postman or `wscat`.  
⚠️ Never enable `USE_TEST_AUTH` in staging or production.

---

## 6 · Security Enhancements

* JWKS keys cached & rate-limited (5 req/min) – prevents DoS against Cognito.
* Fallback to test tokens only when **both** `NODE_ENV=development` _and_ `USE_TEST_AUTH=true`.
* Redis session key refreshed per request; logout simply `DEL session:<id>:<type>`.
* Detailed error messages suppressed in production (generic 401/403).

---

## 7 · Migration Checklist

- [x] Add all Cognito env vars to CI secret store (GitHub, AWS Parameter Store, etc.)  
- [x] Deploy Redis in production (if not already)  
- [x] Remove old hard-coded `JWT_SECRET` usage in clients (front-end updated)  
- [x] Smoke-test REST endpoints with patient & nurse tokens  
- [x] Smoke-test WebSocket handshake + messaging  
- [x] Monitor `/metrics` for `chat_auth_failure_total` (from monitoring middleware)  

---

## 8 · Troubleshooting

| Error | Typical Cause | Fix |
|-------|---------------|-----|
| `Token verification failed with all user pools` | Wrong pool IDs / client IDs | Check env vars vs Cognito console |
| `Session expired` | Redis evicted key | Ensure Redis persistence & memory limits |
| WS closes code 1008 on connect | Token missing in query or header | Append `?token=...&userId=...&userType=...` |
| `Invalid development token` | `USE_TEST_AUTH=false` | Set it to `true` in local `.env` |

---

## 9 · References

* AWS Cognito JWKS: `https://cognito-idp.<region>.amazonaws.com/<poolId>/.well-known/jwks.json`
* RFC 7519 – JSON Web Token
* `jwks-rsa` npm package docs
* Nurserv core API implementation (`nurserv-c-api/src/middleware/auth.js`)
