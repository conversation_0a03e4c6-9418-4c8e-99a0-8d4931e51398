# Frontend ↔ Backend Mismatch Analysis  
chat-room-api × playful-ui-architect / nurserv-c-web  
July 2025

---

## 1 · Executive Summary
The nurse (`playful-ui-architect`) and patient (`nurserv-c-web`) apps were wired to the new **chat-room-api**, however request/response schemas, WebSocket events and environment settings drifted.  
The result is authentication failures, `400` validation errors and abrupt WebSocket disconnects.  
This document pin-points every incompatibility and recommends one canonical contract.

---

## 2 · Environment / URL Mismatches

| Concern | Nurse app | Patient app | Backend | Effect |
|---------|-----------|-------------|---------|--------|
| **REST base URL** | `http://localhost:8004` | `http://localhost:8004/` (trailing slash) | Exposed on `PORT` (8004) | Works, but duplicate `/` in some requests |
| **WS base URL** | `ws://localhost:8004/ws` (nurse) | `ws://localhost:8004` (patient, missing `/ws`) | Listens on `/ws` path | Patient WS fails: 404 |
| **Query params** | `?token & userId & userType` | same | Backend uses `token`, ignores `userId/userType` for auth but **needs them for connection info** | Missing values in backend -> `undefined` fields |
| **PORT drift** | Docs/examples 8003 | Env 8004 | Server started on `.env` PORT | Old hard-coded 8003 returns ECONNREFUSED |

**Fix**  
1. Adopt `CHAT_WS_URL=ws://localhost:8004/ws` in both front-ends.  
2. Always include `userId` & `userType` in query; backend must treat them as required.

---

## 3 · Authentication Inconsistencies

| Item | Frontend behaviour | Backend expectation | Issue |
|------|--------------------|---------------------|-------|
| **Token type** | Supplies Cognito *idToken* (`RS256`) | Accepts RS256 when `USE_TEST_AUTH=false` | OK |
| **Session** | No pre-login call to seed Redis | Requires `session:<sub>:<type>` | 401 “Session expired” |
| **USE_TEST_AUTH** | Shipped disabled | Devs sometimes enable ➜ HS256 | If enabled with real token → `invalid algorithm` |
| **Redis host** | Front-end agnostic | `.env` points to remote `*************:5432` TLS=false | Fails locally unless Redis is reachable |

**Fix**  
- Call lightweight `/api/chat/conversations` immediately after login to create session.  
- Provide local Redis container (`localhost:6379`) and update `.env.development`.  

---

## 4 · REST API Contract Mismatches

### 4.1 Create Conversation

| Field | Front-end sends | Backend `createConversation` wants |
|-------|-----------------|-------------------------------------|
| `customerId` | ✅ | ✅ (required if nurse) |
| `patientName` | ✅ | **Not recognised** |
| `nurseId` | ❌ (patient doesn’t send) | **Required** if patient |
| `title` | ❌ | Optional |

Result: **400** “Nurse ID is required”.

**Resolution**  
Patient app must send `{ nurseId, customerId, title? }`, backend should ignore `patientName` and return it later from DB.

---

### 4.2 Send Message

| Property | Front-end (`sendMessage`) | Backend (`sendMessage`) |
|----------|---------------------------|-------------------------|
| Text key | `content` | `message` |
| Type key | `type` (`text`, …) | `messageType` (`text`, `image`…) |

Backend stores empty message; front-end never receives echo.

**Resolution**  
Choose one naming convention (recommend **`content` + `contentType`**) and align both sides.

---

### 4.3 Response Shapes

| Endpoint | Backend response | Front-end expects | Gap |
|----------|------------------|-------------------|-----|
| `POST /conversations` | `{ success, message, data: { conversationId, ... } }` | `{ success, conversation }` | Extra `data` wrapper |
| `GET /messages` | `{ success, messages, pagination }` | same ✅ | — |
| Pagination keys | `totalPages` | `total_pages` (types declare camelCase) | none (camelCase match) |

**Fix**  
Either unwrap `data` in RTK Query transform or change controller to return `{ conversation: data }`.

---

## 5 · WebSocket Event Schema

| Event | Front-end type | Backend type | Payload mismatch |
|-------|----------------|--------------|------------------|
| Join room | `JOIN_CONVERSATION` | `join_room` | Different case & wording |
| Leave room | `LEAVE_CONVERSATION` | `leave_room` | 〃 |
| Send text | `TEXT_MESSAGE` | `send_message` | 〃 |
| Typing | `TYPING_INDICATOR` | `typing` | 〃 |
| Read receipt | `READ_RECEIPT` | `read_receipt` | 〃 |
| Server ack | `CONNECTION_SUCCESS` | Sends `{type:'connection',status:'connected'}` | Not recognised by hook |

Front-end ignores server events, leading to duplicated messages & no typing indicator.

**Fix options**  
1. **Backend-centric**: modify hook to translate backend verbs → internal constants.  
2. **Frontend-centric**: change `websocket.js` to emit the PascalCase event names.  
3. Provide version tag in query (`?v=1`) to allow breaking change.

---

## 6 · Data Model Differences

| Entity | Front-end interface | Mongoose schema | Notes |
|--------|--------------------|-----------------|-------|
| Conversation | `id` | `_id` (ObjectId) | Transform required |
| Message | `id`, `type`, `status` | `_id`, `messageType`, `status` | Key/enum mismatches |
| User in WS | `senderName` | backend uses `name` optional | Align fields |

---

## 7 · Recommendation Checklist

1. **Unify WebSocket contract**  
   - Decide on snake_case or PASCAL_CASE event names.  
   - Update both `useWebSocket.ts` and `websocket.js`.

2. **Align REST JSON**  
   - Rename backend fields to `content`, `contentType`.  
   - Return `{ conversation }` instead of `{ data: { … } }`.

3. **Bootstrap sessions**  
   - After auth, front-ends call `GET /api/chat/conversations?page=1&limit=1` to seed Redis.

4. **Environment sanity**  
   - Single `.env.development` with `PORT=8004`, `REDIS_HOST=localhost`, `USE_TEST_AUTH=false`.

5. **Strict typing**  
   - Export OpenAPI spec from backend; generate RTK Query types automatically.

6. **Versioning**  
   - Add `X-API-Version: 1` header; reject mismatched versions to avoid silent drift.

---

## 8 · Next Actions

| Owner | Task | Due |
|-------|------|-----|
| Backend | Refactor controller field names & WS event names | 2 days |
| Frontend | Update RTK Query endpoints & WebSocket hook mapping | 2 days |
| DevOps | Provide Redis in docker-compose & identical PORT across services | immediate |
| QA | Regression test chat E2E (REST + WS) | after fixes |

---

*Prepared by: Engineering Integration Team*  
*References: commit 7d3c09e (chat-room-api) & 9f4a21b (playful-ui-architect)*  
