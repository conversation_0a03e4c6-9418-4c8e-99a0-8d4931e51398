# MySQL to MongoDB Migration Guide

This guide provides step-by-step instructions for migrating the Chat Room API from MySQL to MongoDB.

## Overview

The migration involves:
- Replacing MySQL with MongoDB as the primary database
- Updating database schemas to use MongoDB collections
- Modifying queries to use MongoDB aggregation and find operations
- Updating configuration and environment variables
- Maintaining data integrity during migration

## Prerequisites

- MongoDB 4.4 or higher installed
- Existing MySQL database with chat data (if migrating existing data)
- Node.js 14 or higher
- Redis server running

## Migration Steps

### 1. Install MongoDB Dependencies

```bash
npm install mongodb mongoose
npm uninstall mysql2
```

### 2. Update Environment Variables

Update your `.env` file:

```env
# Remove MySQL configuration
# DB_HOST=localhost
# DB_USER=root
# DB_PASSWORD=your_password
# DB_NAME=chat_room_db

# Add MongoDB configuration
MONGODB_URI=mongodb://localhost:27017/chat_room_db
MONGODB_OPTIONS={"useNewUrlParser":true,"useUnifiedTopology":true}
```

### 3. Database Schema Changes

#### MySQL Tables → MongoDB Collections

| MySQL Table | MongoDB Collection | Key Changes |
|-------------|-------------------|-------------|
| `conversations` | `conversations` | - `id` → `_id` (ObjectId)<br>- `patient_id` → `customerId`<br>- `nurse_id` → `nurseId`<br>- `message_count` → `messageCount`<br>- `updated_at` → `lastMessageAt` |
| `messages` | `messages` | - `id` → `_id` (ObjectId)<br>- `conversation_id` → `conversationId` (ObjectId)<br>- `sender_id` → `senderId`<br>- `sender_type` → `senderType` |
| `chat_audit_log` | `auditlogs` | - `id` → `_id` (ObjectId)<br>- `user_id` → `userId`<br>- `user_type` → `userType`<br>- `resource_id` → `resourceId` |

### 4. Data Migration (Optional)

If you have existing MySQL data to migrate:

#### 4.1 Export MySQL Data

```bash
# Export conversations
mysqldump -u root -p chat_room_db conversations > conversations.sql

# Export messages
mysqldump -u root -p chat_room_db messages > messages.sql

# Export audit logs
mysqldump -u root -p chat_room_db chat_audit_log > audit_logs.sql
```

#### 4.2 Create Migration Script

Create `database/migrate-mysql-to-mongodb.js`:

```javascript
const mysql = require('mysql2/promise');
const mongoose = require('mongoose');
require('dotenv').config();

const { Conversation, Message, AuditLog } = require('../src/models');

async function migrateData() {
  try {
    // Connect to MySQL
    const mysqlConnection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });

    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('Connected to both databases');

    // Migrate conversations
    const [conversations] = await mysqlConnection.execute('SELECT * FROM conversations');
    for (const conv of conversations) {
      const conversation = new Conversation({
        customerId: conv.patient_id,
        nurseId: conv.nurse_id,
        title: conv.title,
        status: conv.status,
        messageCount: 0, // Will be updated after messages migration
        lastMessageAt: conv.updated_at,
        createdAt: conv.created_at,
        updatedAt: conv.updated_at
      });
      await conversation.save();
    }
    console.log(`Migrated ${conversations.length} conversations`);

    // Migrate messages
    const [messages] = await mysqlConnection.execute('SELECT * FROM messages');
    for (const msg of messages) {
      const conversation = await Conversation.findOne({
        customerId: msg.patient_id || null,
        nurseId: msg.nurse_id || null
      });
      
      if (conversation) {
        const message = new Message({
          conversationId: conversation._id,
          senderId: msg.sender_id,
          senderType: msg.sender_type,
          message: msg.message,
          messageType: msg.message_type,
          status: msg.status || 'sent',
          createdAt: msg.created_at,
          updatedAt: msg.updated_at
        });
        await message.save();
      }
    }
    console.log(`Migrated ${messages.length} messages`);

    // Update conversation message counts
    const conversationsToUpdate = await Conversation.find({});
    for (const conv of conversationsToUpdate) {
      const messageCount = await Message.countDocuments({ conversationId: conv._id });
      await Conversation.findByIdAndUpdate(conv._id, { messageCount });
    }

    // Migrate audit logs
    const [auditLogs] = await mysqlConnection.execute('SELECT * FROM chat_audit_log');
    for (const log of auditLogs) {
      const auditLog = new AuditLog({
        action: log.action,
        resource: log.resource,
        resourceId: log.resource_id,
        userId: log.user_id,
        userType: log.user_type,
        details: JSON.parse(log.details || '{}'),
        ipAddress: log.ip_address,
        userAgent: log.user_agent,
        timestamp: log.timestamp,
        sessionId: log.session_id,
        createdAt: log.created_at,
        updatedAt: log.updated_at
      });
      await auditLog.save();
    }
    console.log(`Migrated ${auditLogs.length} audit logs`);

    console.log('Migration completed successfully!');

  } catch (error) {
    console.error('Migration error:', error);
  } finally {
    await mysqlConnection.end();
    await mongoose.disconnect();
  }
}

migrateData();
```

#### 4.3 Run Migration

```bash
node database/migrate-mysql-to-mongodb.js
```

### 5. Update Application Code

The following files have been updated for MongoDB:

- `src/config/database.js` - MongoDB connection
- `src/models/` - New MongoDB schemas
- `src/middleware/auth.js` - Updated conversation validation
- `src/server.js` - MongoDB connection initialization

### 6. Initialize New Database

If starting fresh without migration:

```bash
npm run init:db
```

### 7. Test the Migration

```bash
# Start the application
npm run dev

# Test API endpoints
npm run test:api
```

## Key Differences

### Query Changes

#### MySQL (Old)
```sql
SELECT * FROM conversations WHERE patient_id = ? ORDER BY updated_at DESC
```

#### MongoDB (New)
```javascript
await Conversation.find({ customerId }).sort({ lastMessageAt: -1 })
```

### ID Handling

#### MySQL (Old)
```javascript
const conversationId = uuidv4();
```

#### MongoDB (New)
```javascript
const conversation = await Conversation.createConversation(customerId, nurseId, title);
const conversationId = conversation._id.toString();
```

### Relationships

#### MySQL (Old)
```sql
SELECT m.* FROM messages m 
JOIN conversations c ON m.conversation_id = c.id 
WHERE c.id = ?
```

#### MongoDB (New)
```javascript
await Message.find({ conversationId }).populate('conversation')
```

## Performance Optimizations

### Indexes

MongoDB automatically creates indexes for:
- `_id` field (primary key)
- Text search on message content
- Compound indexes for efficient queries

### Caching

Redis caching strategy remains the same:
- Active chats: 1 hour TTL
- Chat history: 24 hours TTL
- User sessions: 30 minutes TTL

## Backup Strategy

### MongoDB Backup

```bash
# Create backup
mongodump --db chat_room_db --out ./backups/

# Restore backup
mongorestore --db chat_room_db ./backups/chat_room_db/
```

### Automated Backup Script

Create `scripts/backup-mongodb.js`:

```javascript
const { exec } = require('child_process');
const path = require('path');

const backupDir = path.join(__dirname, '../backups');
const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

exec(`mongodump --db chat_room_db --out ${backupDir}/${timestamp}`, (error, stdout, stderr) => {
  if (error) {
    console.error('Backup failed:', error);
    return;
  }
  console.log('Backup completed successfully');
});
```

## Rollback Plan

If migration fails:

1. **Keep MySQL data**: Don't delete MySQL database immediately
2. **Test thoroughly**: Verify all functionality before removing MySQL
3. **Gradual rollout**: Deploy to staging environment first
4. **Backup strategy**: Maintain MongoDB backups from day one

## Monitoring

### MongoDB Metrics to Monitor

- Connection pool usage
- Query performance
- Index usage
- Memory usage
- Disk space

### Health Checks

The API includes health check endpoints:
- `/health` - Overall system health
- `/api/chat/conversations/active-count` - Database connectivity test

## Troubleshooting

### Common Issues

1. **Connection Timeout**
   - Check MongoDB service status
   - Verify connection string
   - Check firewall settings

2. **Index Creation Failures**
   - Ensure sufficient disk space
   - Check MongoDB user permissions
   - Verify index syntax

3. **Data Migration Errors**
   - Check data types compatibility
   - Verify foreign key relationships
   - Review error logs

### Support

For migration issues:
1. Check MongoDB logs: `/var/log/mongodb/mongod.log`
2. Review application logs
3. Test with sample data first
4. Use MongoDB Compass for data inspection

## Conclusion

The migration to MongoDB provides:
- Better performance for chat applications
- Flexible schema evolution
- Built-in text search capabilities
- Horizontal scaling potential
- Reduced operational complexity

Follow this guide step-by-step to ensure a smooth migration process. 