# Chat Room API - Patient-Nurse Communication System (MongoDB)

A streamlined REST API for secure patient-nurse communication with MongoDB storage, caching, and audit trails for scrutiny purposes. Designed to work with existing patient and nurse management systems.

## 🆕 Recent Updates

### One-on-One Communication Fixes
The API has been updated to provide robust one-on-one communication between nurses and patients:

- ✅ **Field Name Compatibility**: Supports both legacy (`message`, `messageType`) and new (`content`, `type`) field formats
- ✅ **Enhanced Authentication**: Fixed development mode authentication issues
- ✅ **Improved Conversation Logic**: Better duplicate conversation prevention
- ✅ **WebSocket One-on-One Messaging**: Dedicated functions for direct participant communication
- ✅ **Available Participants**: New endpoints to discover available nurses/patients
- ✅ **Comprehensive Testing**: Full test coverage for one-on-one communication

For detailed information about the fixes, see [ONE_ON_ONE_COMMUNICATION_FIXES.md](ONE_ON_ONE_COMMUNICATION_FIXES.md).

## Features

- **Secure Authentication**: JWT-based authentication with role-based access control
- **Real-time Chat**: Patient-nurse messaging with message history
- **One-on-One Communication**: Direct messaging between specific nurse-patient pairs
- **Database Storage**: MongoDB with comprehensive audit trails
- **Caching**: Redis-based caching for improved performance
- **Message Search**: Full-text search within conversations
- **Statistics & Analytics**: Detailed conversation statistics for scrutiny
- **Rate Limiting**: API rate limiting for security
- **Validation**: Input validation and sanitization
- **Audit Logging**: Complete audit trail for compliance
- **Integration Ready**: Works with existing patient/nurse management APIs

## Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: MongoDB with Mongoose ODM
- **Cache**: Redis
- **Authentication**: JWT
- **Validation**: Express-validator
- **Security**: Helmet, CORS, Rate limiting

## Prerequisites

- Node.js (v14 or higher)
- MongoDB (v4.4 or higher)
- Redis (v6.0 or higher)
- Existing patient and nurse management APIs

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd chat-room-api
   ```

2. **Quick Start (Recommended)**
   ```bash
   # Use the development startup script
   ./start-dev.sh
   ```
   This script will:
   - Check for required dependencies (Node.js, npm)
   - Verify MongoDB and Redis status
   - Install dependencies if needed
   - Create .env file from template
   - Start the development server

3. **Manual Installation**
   ```bash
   # Install dependencies
   npm install
   
   # Set up environment variables
   cp env.example .env
   ```
   Edit `.env` file with your configuration:
   ```env
   PORT=8080
   MONGODB_URI=mongodb://localhost:27017/chat_room_db
   MONGODB_OPTIONS={"useNewUrlParser":true,"useUnifiedTopology":true}
   REDIS_HOST=localhost
   REDIS_PORT=6379
   JWT_SECRET=your_super_secret_jwt_key_here
   USE_TEST_AUTH=true
   JWT_TEST_SECRET=test-secret
   ```

4. **Set up MongoDB**
   ```bash
   # Start MongoDB service
   brew services start mongodb-community
   
   # Initialize database with sample data
   npm run init:db
   ```

5. **Start Redis server**
   ```bash
   brew services start redis
   ```

6. **Start the application**
   ```bash
   npm run dev
   ```

## API Endpoints

### Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

### Core Endpoints

#### Conversations

**Create Conversation**
```http
POST /api/chat/conversations
Content-Type: application/json

{
  "title": "Medical Consultation",
  "nurseId": "nurse-uuid-from-your-api"  // Required for patients
  // OR
  "customerId": "patient-uuid-from-your-api"  // Required for nurses
}
```

**Get User Conversations**
```http
GET /api/chat/conversations?page=1&limit=20
```

**Get Conversation Details**
```http
GET /api/chat/conversations/:conversationId
```

**Mark Conversation Inactive**
```http
PATCH /api/chat/conversations/:conversationId/inactive
```

**Archive Conversation**
```http
PATCH /api/chat/conversations/:conversationId/archive
```

**Get Active Conversations Count**
```http
GET /api/chat/conversations/active-count
```

#### Available Participants

**Get Available Nurses (for patients)**
```http
GET /api/chat/nurses/available
Authorization: Bearer <patient_token>
```

**Get Available Patients (for nurses)**
```http
GET /api/chat/patients/available
Authorization: Bearer <nurse_token>
```

#### Messages

**Send Message**
```http
POST /api/chat/conversations/:conversationId/messages
Content-Type: application/json

{
  "content": "Hello, I have a question about my medication.",  // New format
  "type": "text"  // New format
}
// OR
{
  "message": "Hello, I have a question about my medication.",  // Legacy format
  "messageType": "text"  // Legacy format
}
```

**Get Messages**
```http
GET /api/chat/conversations/:conversationId/messages?page=1&limit=50
```

**Search Messages**
```http
GET /api/chat/conversations/:conversationId/messages/search?q=medication
```

**Mark Messages as Read**
```http
PATCH /api/chat/conversations/:conversationId/messages/read
```

#### Analytics & Scrutiny

**Get Conversation Statistics**
```http
GET /api/chat/conversations/:conversationId/stats
```

Returns detailed statistics including:
- Total messages
- Patient vs nurse message counts
- Unread message count
- Conversation timeline

**Get Conversation Audit Logs**
```http
GET /api/chat/conversations/:conversationId/audit-logs?page=1&limit=50
```

## Database Schema

### MongoDB Collections

1. **conversations** - Chat conversations with embedded metadata
2. **messages** - Individual messages with full-text search
3. **auditlogs** - Comprehensive audit trail for compliance

### Schema Features

- **Flexible Schema**: Easy to add new fields without migrations
- **Embedded Documents**: Efficient data storage and retrieval
- **Text Indexes**: Full-text search capabilities
- **Compound Indexes**: Optimized query performance
- **Audit Trail**: Complete activity logging

### Audit Features

- Automatic logging of all conversation and message activities
- IP address and user agent tracking
- Detailed metadata storage
- Comprehensive audit trail for compliance

## Integration with Existing Systems

This API is designed to work with your existing patient and nurse management systems:

1. **Patient IDs**: Use patient UUIDs from your patient management API
2. **Nurse IDs**: Use nurse UUIDs from your nurse management API
3. **User Authentication**: JWT tokens should contain user type and ID
4. **User Information**: Fetch user details from your existing APIs as needed

## Caching Strategy

### Redis Cache TTL
- **Active Chats**: 1 hour
- **Chat History**: 24 hours
- **User Sessions**: 30 minutes
- **Inactive Chats**: 7 days

### Cache Invalidation
- Automatic cache invalidation on message send
- Pattern-based cache clearing for related data
- TTL-based automatic expiration

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Role-based Access**: Patient/nurse role validation
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: Comprehensive input sanitization
- **CORS Protection**: Configurable CORS settings
- **Helmet Security**: Security headers
- **SQL Injection Protection**: Parameterized queries

## Error Handling

The API returns consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": [] // Validation errors if applicable
}
```

## Testing

### Quick Start
```bash
# Run all tests
npm run test:all

# Run specific test suites
npm run test:quick          # Quick health check
npm run test:one-on-one     # One-on-one communication tests
npm run test:comprehensive  # Comprehensive API tests
npm run test:websocket      # WebSocket tests
```

### Test Coverage
- ✅ **Health Check**: Server connectivity and basic functionality
- ✅ **One-on-One Communication**: Nurse-patient direct messaging
- ✅ **Authentication**: JWT token validation and session management
- ✅ **Conversation Management**: Create, read, update conversations
- ✅ **Message Handling**: Send, receive, search messages with both field formats
- ✅ **WebSocket Communication**: Real-time messaging and room management
- ✅ **Error Handling**: Validation errors and edge cases

## Development

### Running Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev
```

### Production Mode
```bash
npm start
```

### API Testing
```bash
npm run test:api
```

## Monitoring & Health Checks

### Health Check Endpoint
```http
GET /health
```

Returns server status, uptime, and environment information.

## Performance Optimization

- **Database Indexing**: Optimized indexes for common queries
- **Redis Caching**: Frequently accessed data cached
- **Pagination**: Efficient pagination for large datasets
- **Connection Pooling**: Database connection pooling
- **Compression**: Response compression enabled

## Compliance & Scrutiny

### Audit Trail
- All conversations and messages are permanently stored
- Complete audit log with user actions
- IP address and timestamp tracking
- Message content preservation for medical compliance

### Data Retention
- Messages: Permanent storage
- Audit logs: Permanent storage
- User sessions: Configurable TTL
- Cache data: Automatic expiration

## Future Enhancements

- [ ] WebSocket support for real-time messaging
- [ ] File upload and media sharing
- [ ] Message reactions and emojis
- [ ] Group chat support
- [ ] Message encryption
- [ ] Push notifications
- [ ] Advanced analytics dashboard
- [ ] Export functionality for audit reports

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the ISC License.

## Support

For support and questions, please contact the development team.