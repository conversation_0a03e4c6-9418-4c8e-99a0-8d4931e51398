# Simple Unread Count Solution

## Problem
`429 Too Many Requests` error when calling `http://localhost:8004/api/chat/conversations` repeatedly from the UI.

## Root Cause
The frontend is likely polling the conversations endpoint frequently to get unread counts, which triggers rate limiting.

## Simple Solution

### What's Already Available ✅
1. **User conversations already include unread counts** - The `GET /api/chat/conversations` endpoint already returns `unreadCount` for each conversation
2. **WebSocket infrastructure** - Complete WebSocket system for real-time updates
3. **Mark as read functionality** - Existing endpoint and WebSocket broadcasting

### What You Need to Do (Frontend)

#### 1. Stop Polling
Instead of polling `/api/chat/conversations` every few seconds, only call it when:
- User opens the app
- User switches conversations
- User manually refreshes

#### 2. Use WebSocket for Real-time Updates
Listen for WebSocket messages to get real-time updates:

```javascript
// When new message arrives via WebSocket
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  if (message.type === 'new_message') {
    // Update conversation list with new unread count
    updateConversationUnreadCount(message.conversationId, +1);
  }
  
  if (message.type === 'messages_read') {
    // Update conversation list with new unread count
    updateConversationUnreadCount(message.conversationId, 0);
  }
};
```

#### 3. Update UI Efficiently
```javascript
function updateConversationUnreadCount(conversationId, newCount) {
  const conversationElement = document.querySelector(`[data-conversation-id="${conversationId}"]`);
  if (conversationElement) {
    const badge = conversationElement.querySelector('.unread-badge');
    if (badge) {
      badge.textContent = newCount;
      badge.style.display = newCount > 0 ? 'block' : 'none';
    }
  }
}
```

### Benefits of This Approach

✅ **No Rate Limiting** - No frequent HTTP requests  
✅ **Real-time Updates** - WebSocket provides instant updates  
✅ **Better Performance** - Reduced server load  
✅ **Better UX** - Instant feedback  
✅ **Minimal Changes** - Uses existing infrastructure  

### Implementation Steps

1. **Remove polling intervals** from your frontend code
2. **Listen to WebSocket messages** for real-time updates
3. **Update UI immediately** when WebSocket messages arrive
4. **Only call HTTP endpoints** when user explicitly requests data

### Example Frontend Code

```javascript
// Initialize WebSocket connection
const ws = new WebSocket('ws://localhost:8004/ws');

// Handle real-time updates
ws.onmessage = (event) => {
  const message = JSON.parse(event.data);
  
  switch (message.type) {
    case 'new_message':
      // Increment unread count for this conversation
      incrementUnreadCount(message.conversationId);
      break;
      
    case 'messages_read':
      // Reset unread count for this conversation
      resetUnreadCount(message.conversationId);
      break;
  }
};

// Only load conversations when needed
async function loadConversations() {
  const response = await fetch('/api/chat/conversations', {
    headers: { Authorization: `Bearer ${token}` }
  });
  const data = await response.json();
  
  // Update UI with conversations and their unread counts
  data.data.conversations.forEach(conv => {
    displayConversation(conv);
  });
}

// Call this only when user opens app or refreshes
loadConversations();
```

This simple approach eliminates the rate limiting issue by using the existing WebSocket infrastructure for real-time updates instead of polling HTTP endpoints. 