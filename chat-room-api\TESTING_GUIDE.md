# 🧪 Chat Room API Testing Guide

This guide will help you test all endpoints of your Chat Room API before integrating with your UI.

## 📋 Prerequisites

1. **Start MongoDB**: `brew services start mongodb/brew/mongodb-community`
2. **Start Redis**: `brew services start redis`
3. **Install dependencies**: `npm install`
4. **Create .env file**: `cp env.example .env`
5. **Start the server**: `npm run dev`

## 🚀 Quick Start Testing

### 1. Generate Test Tokens
```bash
npm run test:generate-tokens
```
This creates JWT tokens for test patients and nurses and stores them in Redis.

### 2. Run All Tests
```bash
npm run test:all
```
This runs comprehensive API tests and WebSocket tests.

### 3. Run Individual Test Suites
```bash
# API tests only
npm run test:comprehensive

# WebSocket tests only
npm run test:websocket

# Original basic tests
npm run test:api
```

## 🔍 Manual Testing with cURL

### 1. Health Check
```bash
curl -X GET http://localhost:8080/health
```

### 2. Generate Test Token (Manual)
```bash
# First, generate tokens
node test/generate-test-tokens.js

# Copy the token from the output and use it in subsequent requests
TOKEN="your_generated_token_here"
```

### 3. Test Authentication
```bash
# Test with valid token
curl -X GET http://localhost:8080/api/chat/conversations \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"

# Test without token (should fail)
curl -X GET http://localhost:8080/api/chat/conversations \
  -H "Content-Type: application/json"
```

### 4. Test Conversation Creation
```bash
# Create conversation as patient
curl -X POST http://localhost:8080/api/chat/conversations \
  -H "Authorization: Bearer $PATIENT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Medical Consultation",
    "nurseId": "test-nurse-001"
  }'

# Create conversation as nurse
curl -X POST http://localhost:8080/api/chat/conversations \
  -H "Authorization: Bearer $NURSE_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Patient Follow-up",
    "customerId": "test-patient-001"
  }'
```

### 5. Test Message Sending
```bash
# Send a message
curl -X POST http://localhost:8080/api/chat/conversations/CONVERSATION_ID/messages \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello, this is a test message!",
    "messageType": "text"
  }'
```

### 6. Test Message Retrieval
```bash
# Get messages from conversation
curl -X GET "http://localhost:8080/api/chat/conversations/CONVERSATION_ID/messages?page=1&limit=20" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
```

### 7. Test Message Search
```bash
# Search messages
curl -X GET "http://localhost:8080/api/chat/conversations/CONVERSATION_ID/messages/search?q=test" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
```

### 8. Test Conversation Statistics
```bash
# Get conversation stats
curl -X GET http://localhost:8080/api/chat/conversations/CONVERSATION_ID/stats \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json"
```

## 🔌 WebSocket Testing

### 1. Manual WebSocket Testing with wscat
```bash
# Install wscat globally
npm install -g wscat

# Connect with token
wscat -c "ws://localhost:8080?token=YOUR_JWT_TOKEN"

# Send join room message
{"type": "join_room", "conversationId": "CONVERSATION_ID"}

# Send message
{"type": "send_message", "conversationId": "CONVERSATION_ID", "content": "Hello via WebSocket!", "messageType": "text"}

# Send typing indicator
{"type": "typing", "conversationId": "CONVERSATION_ID", "isTyping": true}

# Send read receipt
{"type": "read_receipt", "conversationId": "CONVERSATION_ID", "messageId": "MESSAGE_ID"}
```

### 2. Browser Console Testing
```javascript
// Connect to WebSocket
const ws = new WebSocket('ws://localhost:8080?token=YOUR_JWT_TOKEN');

// Listen for messages
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};

// Send join room
ws.send(JSON.stringify({
  type: 'join_room',
  conversationId: 'CONVERSATION_ID'
}));

// Send message
ws.send(JSON.stringify({
  type: 'send_message',
  conversationId: 'CONVERSATION_ID',
  content: 'Hello from browser!',
  messageType: 'text'
}));
```

## 📊 Testing Checklist

### ✅ Authentication & Authorization
- [ ] JWT token generation
- [ ] Token validation
- [ ] Session management
- [ ] Unauthorized access rejection
- [ ] Role-based access control

### ✅ Conversation Management
- [ ] Create conversation (patient)
- [ ] Create conversation (nurse)
- [ ] Get user conversations
- [ ] Get conversation details
- [ ] Mark conversation inactive
- [ ] Get active conversations count

### ✅ Message Operations
- [ ] Send text message
- [ ] Send different message types
- [ ] Get messages with pagination
- [ ] Search messages
- [ ] Message validation

### ✅ Real-Time Features
- [ ] WebSocket connection
- [ ] Join/leave room
- [ ] Real-time message sending
- [ ] Typing indicators
- [ ] Read receipts
- [ ] Message broadcasting

### ✅ Error Handling
- [ ] Invalid conversation access
- [ ] Invalid message format
- [ ] Missing required fields
- [ ] Server errors
- [ ] Network errors

### ✅ Performance
- [ ] Response times
- [ ] Concurrent connections
- [ ] Message delivery speed
- [ ] Memory usage

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   ```bash
   # Check if MongoDB is running
   brew services list | grep mongodb
   
   # Start MongoDB if not running
   brew services start mongodb/brew/mongodb-community
   ```

2. **Redis Connection Failed**
   ```bash
   # Check if Redis is running
   brew services list | grep redis
   
   # Start Redis if not running
   brew services start redis
   ```

3. **JWT Token Issues**
   ```bash
   # Regenerate tokens
   npm run test:generate-tokens
   ```

4. **WebSocket Connection Failed**
   ```bash
   # Check if server is running
   curl http://localhost:8080/health
   
   # Check WebSocket endpoint
   curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" -H "Sec-WebSocket-Version: 13" -H "Sec-WebSocket-Key: x3JJHMbDL1EzLkh9GBhXDw==" http://localhost:8080
   ```

### Debug Mode

Enable debug logging by setting environment variables:
```bash
export DEBUG=chat-api:*
export LOG_LEVEL=debug
npm run dev
```

## 📈 Performance Testing

### Load Testing with Artillery
```bash
# Install Artillery
npm install -g artillery

# Run load test
artillery run test/load-test.yml
```

### Memory and CPU Monitoring
```bash
# Monitor server process
top -pid $(lsof -ti:8080)

# Monitor memory usage
node --inspect src/server.js
```

## 🔒 Security Testing

### Test Cases
- [ ] SQL injection attempts
- [ ] XSS attempts
- [ ] CSRF attempts
- [ ] Rate limiting
- [ ] Input validation
- [ ] Authentication bypass attempts

### Security Headers Check
```bash
curl -I http://localhost:8080/health
```

## 📝 Test Reports

After running tests, check the console output for:
- ✅ Success indicators
- ❌ Error messages
- 📊 Performance metrics
- 🔍 Debug information

## 🎯 Next Steps

Once all tests pass:
1. ✅ API endpoints are working correctly
2. ✅ WebSocket real-time messaging is functional
3. ✅ Authentication and authorization are secure
4. ✅ Error handling is robust
5. ✅ Performance is acceptable

You're ready to integrate with your UI! 🚀

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section
2. Review server logs
3. Verify environment configuration
4. Test individual components
5. Check MongoDB and Redis connections 