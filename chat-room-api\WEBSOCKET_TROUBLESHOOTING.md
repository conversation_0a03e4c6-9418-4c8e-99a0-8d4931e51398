# WebSocket Troubleshooting Guide  
chat-room-api · playful-ui-architect / nurserv-c-web  

_Aim: restore real-time patient ⇄ nurse chat when the socket refuses to connect, drops unexpectedly, or fails to deliver messages._

---

## 1 · Quick Checklist

| ✅ | Item | Command / Where to look |
|----|------|------------------------|
|   | API server running on expected port (`PORT`) | `curl http://localhost:8005/health` |
|   | WebSocket URL correct (`ws://host:PORT/ws`) | Browser console → value logged by `useWebSocket` |
|   | Query params present (`token,userId,userType`) | Inspect `ws.url` in console |
|   | Valid Cognito token (not expired) | `jwt.io` or `npx jose decode` |
|   | Redis reachable (`localhost:6379`) | `redis-cli ping` → `PONG` |
|   | No port clash (another service on 8005) | `lsof -i :8005` |
|   | CORS / WS allowed by proxy / nginx | Check `nginx.conf` `upgrade` rules |
|   | Firewall allows port 8005 external (prod) | `telnet host 8005` |

Tick everything before diving deeper.

---

## 2 · Typical Error Messages & Fixes

| Browser Console Error | Meaning | Resolution |
|-----------------------|---------|------------|
| `failed: Insufficient resources` | Server closed socket immediately (often Redis down or session missing). | 1. `redis-cli ping` <br>2. Ensure `REDIS_HOST/PORT/PASSWORD` correct in `.env` <br>3. Restart API. |
| `Unexpected response code: 404` | Wrong path (should be `/ws`). | Verify `VITE_CHAT_WS_URL` ends with `/ws`. |
| `Connection closed: 1008 (policy violation)` | Auth failed (token invalid / session expired). | • Check token expiry. <br>• Run `/api/chat/conversations` to refresh session. |
| `net::ERR_CONNECTION_REFUSED` | Nothing listening on that host:port. | Start API (`npm start`) or adjust port. |
| `JsonWebTokenError: invalid algorithm` in server logs | `USE_TEST_AUTH=true` with RS256 token. | Set `USE_TEST_AUTH=false`. |
| `read ECONNRESET` in server logs | Proxy terminated idle connection. | Increase `proxy_read_timeout` / keep-alive. |

---

## 3 · Step-by-Step Debugging

### 3.1 Verify Backend

```bash
# 1. Is the process up?
ps aux | grep 'chat-room-api'

# 2. Health endpoint
curl -s http://localhost:8005/health | jq .

# 3. Check websocket upgrade via curl
curl -i -N -H "Connection: Upgrade" -H "Upgrade: websocket" http://localhost:8005/ws 2>&1 | head
```

Expect `HTTP/1.1 400 Bad Request` (because no token) – proves path is mounted.

### 3.2 Check Redis

```bash
redis-cli -h $REDIS_HOST -p $REDIS_PORT ping
# Should return PONG
```

If not, start local instance:

```bash
docker run -d --name redis-chat -p 6379:6379 redis:alpine
```

### 3.3 Confirm Token & Session

1. Grab `idToken` from localStorage.  
2. Decode: `npx jose decode <TOKEN>`.  
3. Ensure `exp` is in the future.  
4. On server: `redis-cli keys 'session:*:<patient|nurse>'` – you should see a key for the `sub`.

If session missing, perform a quick REST request (e.g., `GET /api/chat/conversations`) to seed it.

### 3.4 Front-End Inspection

1. Browser DevTools → Network → WS → select row → **Messages** tab.  
2. Observe handshake URL – ensure it matches pattern:  
   ```
   ws://localhost:8005/ws?token=<JWT>&userId=<sub>&userType=<patient|nurse>
   ```  
3. Check first server message:  
   `{ "type":"CONNECTION_SUCCESS", "status":"connected" }`  
   Anything else → see previous table.

### 3.5 Use the Automated Test Script

```bash
node test-websocket-connection.js \
  --wsUrl ws://localhost:8005/ws \
  --token "$ID_TOKEN" \
  --userId "$SUB" \
  --userType patient --verbose
```

The script walks through connection, authentication, room join, send/receive. All 5 stages must pass.

---

## 4 · Environment Variable Pitfalls

| Variable | Correct Dev Value | Common Mistake |
|----------|-------------------|----------------|
| `PORT` | 8005 | 8003/8004 left over from docs |
| `REDIS_PORT` | 6379 | 5432 (PostgreSQL) |
| `REDIS_HOST` | localhost | remote host unreachable offline |
| `USE_TEST_AUTH` | false (when using real Cognito) | true → algorithm error |
| `VITE_CHAT_WS_URL` | ws://localhost:8005/ws | missing `/ws` path |

---

## 5 · Server-Side Logging Tips

* Start API with debug:  
  `DEBUG=ws*,chat* npm start`
* Enable verbose WebSocket logging: set `LOG_LEVEL=debug` in `.env`.
* Logs will show:  
  ```
  [ws-auth] Auto-created dev session for 71a3fd8a... (patient)
  joinRoom → conversationId=...
  broadcastMessage → 2 recipients
  ```

Review for clues (e.g., “Session expired”, “Unknown message type”).

---

## 6 · Production Considerations

1. **Reverse proxy headers** – ensure Nginx/NLB preserves `Upgrade` & `Connection: upgrade`.  
2. **TLS** – use `wss://` behind HTTPS termination. Ports often differ (443).  
3. **Scaling** – when running multiple instances, use **Redis** or other pub/sub for cross-node message fan-out (already configured).  
4. **Idle timeout** – AWS ALB defaults 60 s; increase to min 5 min.

---

## 7 · FAQ

**Q:** WebSocket connects but no messages received.  
**A:** Client may not send `JOIN_CONVERSATION` after open. Check `useWebSocket` hook state.

**Q:** Works locally, fails on staging.  
**A:** Likely proxy not forwarding upgrade or security group blocks port.

**Q:** Random disconnects after a few minutes.  
**A:** Heartbeat missing. Verify client sends ping/keep-alive or increase `WS_CLOSE_INACTIVE_AFTER`.

---

## 8 · When All Else Fails

1. Record a HAR file (DevTools → Network → Export).  
2. Attach server logs around the timestamp.  
3. Include output of the test script (`test-websocket-connection.js --verbose`).  
4. Open an issue with the above attachments – label `websocket`.

---

Happy debugging & may your sockets stay open! 🚀
