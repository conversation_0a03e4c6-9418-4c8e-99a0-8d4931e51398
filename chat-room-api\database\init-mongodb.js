const { MongoClient, ServerApiVersion } = require('mongodb');
require('dotenv').config();

const { Conversation, Message, AuditLog } = require('../src/models');

const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/chat_room_db';
console.log(mongoUri);

async function initializeMongoDB() {
  try {
    // Connect to MongoDB

    const client = new MongoClient(mongoUri, {
        serverApi: {
          version: ServerApiVersion.v1,
          strict: true,
          deprecationErrors: true,
        }
      });
    // await mongoose.connect(mongoUri, {
    //     serverApi: {
    //         version: ServerApiVersion.v1,
    //         strict: true,
    //         deprecationErrors: true,
    //       }
    // });
    
    console.log('Connected to MongoDB');
    
    // Clear existing data (optional - comment out in production)
    console.log('Clearing existing data...');
    await Conversation.deleteMany({});
    await Message.deleteMany({});
    await AuditLog.deleteMany({});
    
    // Create indexes
    console.log('Creating indexes...');
    
    // Conversation indexes
    await Conversation.collection.createIndex({ customerId: 1, lastMessageAt: -1 });
    await Conversation.collection.createIndex({ nurseId: 1, lastMessageAt: -1 });
    await Conversation.collection.createIndex({ status: 1, lastMessageAt: -1 });
    
    // Message indexes
    await Message.collection.createIndex({ conversationId: 1, createdAt: -1 });
    await Message.collection.createIndex({ senderId: 1, createdAt: -1 });
    await Message.collection.createIndex({ conversationId: 1, status: 1 });
    await Message.collection.createIndex({ message: 'text' });
    
    // Audit log indexes
    await AuditLog.collection.createIndex({ action: 1, timestamp: -1 });
    await AuditLog.collection.createIndex({ resource: 1, resourceId: 1 });
    await AuditLog.collection.createIndex({ userId: 1, timestamp: -1 });
    await AuditLog.collection.createIndex({ timestamp: -1 });
    
    console.log('Indexes created successfully');
    
    // Create sample data
    console.log('Creating sample data...');
    
    // Sample conversations
    const sampleConversations = [
      {
        customerId: 'patient001',
        nurseId: 'nurse001',
        title: 'General Health Consultation',
        status: 'active',
        messageCount: 5
      },
      {
        customerId: 'patient002',
        nurseId: 'nurse002',
        title: 'Medication Follow-up',
        status: 'active',
        messageCount: 3
      },
      {
        customerId: 'patient003',
        nurseId: 'nurse001',
        title: 'Emergency Consultation',
        status: 'inactive',
        messageCount: 8
      }
    ];
    
    const conversations = await Conversation.insertMany(sampleConversations);
    console.log(`Created ${conversations.length} sample conversations`);
    
    // Sample messages for first conversation
    const sampleMessages = [
      {
        conversationId: conversations[0]._id,
        senderId: 'patient001',
        senderType: 'patient',
        message: 'Hello, I have a question about my medication.',
        messageType: 'text',
        status: 'read'
      },
      {
        conversationId: conversations[0]._id,
        senderId: 'nurse001',
        senderType: 'nurse',
        message: 'Hello! I\'d be happy to help. What medication are you taking?',
        messageType: 'text',
        status: 'read'
      },
      {
        conversationId: conversations[0]._id,
        senderId: 'patient001',
        senderType: 'patient',
        message: 'I\'m taking Lisinopril 10mg daily.',
        messageType: 'text',
        status: 'read'
      },
      {
        conversationId: conversations[0]._id,
        senderId: 'nurse001',
        senderType: 'nurse',
        message: 'That\'s a common blood pressure medication. Are you experiencing any side effects?',
        messageType: 'text',
        status: 'read'
      },
      {
        conversationId: conversations[0]._id,
        senderId: 'patient001',
        senderType: 'patient',
        message: 'Yes, I\'ve been feeling a bit dizzy in the mornings.',
        messageType: 'text',
        status: 'sent'
      }
    ];
    
    const messages = await Message.insertMany(sampleMessages);
    console.log(`Created ${messages.length} sample messages`);
    
    // Update conversation message counts
    for (const conversation of conversations) {
      const messageCount = await Message.countDocuments({ conversationId: conversation._id });
      await Conversation.findByIdAndUpdate(conversation._id, { messageCount });
    }
    
    // Sample audit logs
    const sampleAuditLogs = [
      {
        action: 'create',
        resource: 'conversation',
        resourceId: conversations[0]._id.toString(),
        userId: 'patient001',
        userType: 'patient',
        details: { customerId: 'patient001', nurseId: 'nurse001' },
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      {
        action: 'message_sent',
        resource: 'message',
        resourceId: messages[0]._id.toString(),
        userId: 'patient001',
        userType: 'patient',
        details: { conversationId: conversations[0]._id.toString(), messageType: 'text' },
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      {
        action: 'message_read',
        resource: 'message',
        resourceId: messages[0]._id.toString(),
        userId: 'nurse001',
        userType: 'nurse',
        details: { conversationId: conversations[0]._id.toString() },
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
      }
    ];
    
    const auditLogs = await AuditLog.insertMany(sampleAuditLogs);
    console.log(`Created ${auditLogs.length} sample audit logs`);
    
    console.log('MongoDB initialization completed successfully!');
    console.log('\nSample data created:');
    console.log(`- ${conversations.length} conversations`);
    console.log(`- ${messages.length} messages`);
    console.log(`- ${auditLogs.length} audit logs`);
    
  } catch (error) {
    console.error('Error initializing MongoDB:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run initialization if this file is executed directly
if (require.main === module) {
  initializeMongoDB();
}

module.exports = { initializeMongoDB }; 