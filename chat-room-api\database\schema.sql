-- Chat Room API Database Schema
-- This schema supports patient-nurse chat functionality with audit trails
-- Note: Patient and nurse data should come from separate APIs

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS chat_room_db;
USE chat_room_db;

-- Conversations table
CREATE TABLE IF NOT EXISTS conversations (
    id VARCHAR(36) PRIMARY KEY,
    patient_id VARCHAR(36) NOT NULL,
    nurse_id VARCHAR(36) NOT NULL,
    title VARCHAR(100),
    status ENUM('active', 'inactive', 'archived') DEFAULT 'active',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    category VARCHAR(50),
    tags JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_message_at TIMESTAMP NULL,
    message_count INT DEFAULT 0,
    INDEX idx_patient_id (patient_id),
    INDEX idx_nurse_id (nurse_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_updated_at (updated_at),
    INDEX idx_priority (priority),
    UNIQUE KEY unique_active_conversation (patient_id, nurse_id, status)
);

-- Messages table
CREATE TABLE IF NOT EXISTS messages (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(36) NOT NULL,
    sender_type ENUM('patient', 'nurse') NOT NULL,
    message TEXT NOT NULL,
    message_type ENUM('text', 'image', 'file', 'audio', 'video') DEFAULT 'text',
    file_url VARCHAR(500),
    file_name VARCHAR(255),
    file_size INT,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    is_edited BOOLEAN DEFAULT FALSE,
    edited_at TIMESTAMP NULL,
    original_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_sender_type (sender_type),
    INDEX idx_created_at (created_at),
    INDEX idx_is_read (is_read),
    INDEX idx_message_type (message_type)
);

-- Message reactions table (for future enhancement)
CREATE TABLE IF NOT EXISTS message_reactions (
    id VARCHAR(36) PRIMARY KEY,
    message_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    user_type ENUM('patient', 'nurse') NOT NULL,
    reaction_type ENUM('like', 'love', 'care', 'wow', 'sad', 'angry') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (message_id) REFERENCES messages(id) ON DELETE CASCADE,
    UNIQUE KEY unique_reaction (message_id, user_id, user_type),
    INDEX idx_message_id (message_id),
    INDEX idx_user_id (user_id)
);

-- Conversation participants (for future group chat support)
CREATE TABLE IF NOT EXISTS conversation_participants (
    id VARCHAR(36) PRIMARY KEY,
    conversation_id VARCHAR(36) NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    user_type ENUM('patient', 'nurse') NOT NULL,
    role ENUM('participant', 'moderator', 'admin') DEFAULT 'participant',
    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    left_at TIMESTAMP NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (conversation_id) REFERENCES conversations(id) ON DELETE CASCADE,
    UNIQUE KEY unique_participant (conversation_id, user_id, user_type),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active)
);

-- Audit log table for scrutiny purposes
CREATE TABLE IF NOT EXISTS chat_audit_log (
    id VARCHAR(36) PRIMARY KEY,
    action_type ENUM('conversation_created', 'conversation_updated', 'conversation_deleted', 
                     'message_sent', 'message_edited', 'message_deleted', 'message_read',
                     'user_joined', 'user_left', 'status_changed') NOT NULL,
    user_id VARCHAR(36) NOT NULL,
    user_type ENUM('patient', 'nurse', 'admin', 'system') NOT NULL,
    conversation_id VARCHAR(36),
    message_id VARCHAR(36),
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_action_type (action_type),
    INDEX idx_user_id (user_id),
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_created_at (created_at)
);

-- User sessions table for authentication
CREATE TABLE IF NOT EXISTS user_sessions (
    id VARCHAR(36) PRIMARY KEY,
    user_id VARCHAR(36) NOT NULL,
    user_type ENUM('patient', 'nurse') NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_user_id (user_id),
    INDEX idx_token_hash (token_hash),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
);

-- Create triggers for audit logging
DELIMITER //

-- Trigger to log conversation creation
CREATE TRIGGER audit_conversation_created
AFTER INSERT ON conversations
FOR EACH ROW
BEGIN
    INSERT INTO chat_audit_log (id, action_type, user_id, user_type, conversation_id, new_values, created_at)
    VALUES (UUID(), 'conversation_created', NEW.patient_id, 'patient', NEW.id, JSON_OBJECT('patient_id', NEW.patient_id, 'nurse_id', NEW.nurse_id, 'title', NEW.title), NOW());
END//

-- Trigger to log message sending
CREATE TRIGGER audit_message_sent
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    INSERT INTO chat_audit_log (id, action_type, user_id, user_type, conversation_id, message_id, new_values, created_at)
    VALUES (UUID(), 'message_sent', NEW.sender_id, NEW.sender_type, NEW.conversation_id, NEW.id, JSON_OBJECT('message_type', NEW.message_type, 'message_length', LENGTH(NEW.message)), NOW());
END//

-- Trigger to update conversation message count and last message time
CREATE TRIGGER update_conversation_stats
AFTER INSERT ON messages
FOR EACH ROW
BEGIN
    UPDATE conversations 
    SET message_count = message_count + 1,
        last_message_at = NEW.created_at,
        updated_at = NOW()
    WHERE id = NEW.conversation_id;
END//

DELIMITER ;

-- Create indexes for better performance
CREATE INDEX idx_conversations_composite ON conversations(patient_id, nurse_id, status);
CREATE INDEX idx_messages_composite ON messages(conversation_id, created_at);
CREATE INDEX idx_audit_log_composite ON chat_audit_log(user_id, action_type, created_at);

-- Key optimizations for chat performance
1. Proper indexing on conversation_id, created_at
2. Partitioning messages table by date
3. Caching frequently accessed conversations in Redis
4. Read replicas for analytics queries
5. Connection pooling for high concurrency

-- Redis usage patterns
- Active conversation cache (TTL: 1 hour)
- User session management (TTL: 30 minutes)
- Real-time message delivery status
- Typing indicators and presence
- Rate limiting and throttling 