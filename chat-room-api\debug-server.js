#!/usr/bin/env node
/**
 * Chat API Debug Server
 * ---------------------
 * Comprehensive diagnostic tool for the chat-room-api server.
 * Tests connectivity, authentication, Redis, and WebSocket functionality.
 * 
 * Usage:
 *   node debug-server.js [options]
 * 
 * Options:
 *   --port=<port>         Server port (default: 8004)
 *   --token=<token>       Valid Cognito ID token for authentication
 *   --user-id=<id>        User ID (sub from token)
 *   --user-type=<type>    User type: 'patient' or 'nurse'
 *   --redis-host=<host>   Redis host (default: from .env)
 *   --redis-port=<port>   Redis port (default: from .env)
 *   --verbose             Enable verbose logging
 *   --test-only=<test>    Run only specific test(s): redis,http,ws,auth,all
 *   --help                Show this help message
 * 
 * Example:
 *   node debug-server.js --token="eyJraW..." --user-id="123" --user-type="patient" --verbose
 */

// Core dependencies
const http = require('http');
const https = require('https');
const WebSocket = require('ws');
const { URL } = require('url');
const { promisify } = require('util');
const { createHash } = require('crypto');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Load environment variables
require('dotenv').config();

// Redis client
let redisClient;

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.substring(2).split('=');
    acc[key] = value === undefined ? true : value;
  }
  return acc;
}, {});

// Configuration
const config = {
  port: args.port || process.env.PORT || 8004,
  token: args.token,
  userId: args['user-id'],
  userType: args['user-type'] || 'patient',
  redisHost: args['redis-host'] || process.env.REDIS_HOST || 'localhost',
  redisPort: args['redis-port'] || process.env.REDIS_PORT || 6379,
  redisPassword: process.env.REDIS_PASSWORD,
  redisTLS: process.env.REDIS_TLS === 'true',
  verbose: args.verbose || false,
  testOnly: args['test-only'] || 'all',
  help: args.help || false,
  timeout: 10000, // 10 seconds timeout for tests
};

// Colored console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  underscore: '\x1b[4m',
  blink: '\x1b[5m',
  reverse: '\x1b[7m',
  hidden: '\x1b[8m',
  black: '\x1b[30m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bgBlack: '\x1b[40m',
  bgRed: '\x1b[41m',
  bgGreen: '\x1b[42m',
  bgYellow: '\x1b[43m',
  bgBlue: '\x1b[44m',
  bgMagenta: '\x1b[45m',
  bgCyan: '\x1b[46m',
  bgWhite: '\x1b[47m',
};

// Logger
const logger = {
  info: (...args) => console.log(`${colors.cyan}[INFO]${colors.reset}`, ...args),
  success: (...args) => console.log(`${colors.green}[SUCCESS]${colors.reset}`, ...args),
  error: (...args) => console.error(`${colors.red}[ERROR]${colors.reset}`, ...args),
  warn: (...args) => console.warn(`${colors.yellow}[WARN]${colors.reset}`, ...args),
  debug: (...args) => {
    if (config.verbose) {
      console.log(`${colors.dim}[DEBUG]${colors.reset}`, ...args);
    }
  },
  section: (title) => {
    console.log(`\n${colors.bright}${colors.cyan}=== ${title} ===${colors.reset}\n`);
  },
  result: (test, passed, message) => {
    const status = passed ? `${colors.green}✓ PASS${colors.reset}` : `${colors.red}✗ FAIL${colors.reset}`;
    console.log(`${status} ${test}: ${message}`);
  },
  json: (obj) => {
    console.log(JSON.stringify(obj, null, 2));
  },
};

// Show help and exit
if (config.help) {
  const helpText = fs.readFileSync(__filename, 'utf8')
    .split('\n')
    .slice(1, 25)
    .map(line => line.replace(/^\s*\*\s*/, ''))
    .join('\n');
  
  console.log(helpText);
  process.exit(0);
}

// Validate required parameters for certain tests
if ((config.testOnly === 'all' || config.testOnly === 'auth' || config.testOnly === 'ws') && 
    (!config.token || !config.userId)) {
  logger.error('Authentication tests require --token and --user-id parameters');
  logger.info('Run with --help for usage information');
  process.exit(1);
}

// Initialize Redis client if needed
async function initRedis() {
  try {
    if (!redisClient) {
      const redis = require('redis');
      
      const redisConfig = {
        socket: {
          host: config.redisHost,
          port: config.redisPort,
          tls: config.redisTLS,
        }
      };
      
      if (config.redisPassword) {
        redisConfig.password = config.redisPassword;
      }
      
      logger.debug('Redis config:', { ...redisConfig, password: redisConfig.password ? '***' : undefined });
      redisClient = redis.createClient(redisConfig);
      
      redisClient.on('error', (err) => {
        logger.error('Redis client error:', err);
      });
      
      if (!redisClient.isOpen) {
        logger.debug('Connecting to Redis...');
        await redisClient.connect();
      }
    }
    return redisClient;
  } catch (error) {
    logger.error('Failed to initialize Redis client:', error);
    throw error;
  }
}

// HTTP request helper
async function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const parsedUrl = new URL(url);
    const requestOptions = {
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: options.timeout || config.timeout,
    };
    
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const req = client.request(url, requestOptions, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        let responseBody;
        try {
          responseBody = data.length > 0 ? JSON.parse(data) : null;
        } catch (e) {
          responseBody = data;
        }
        
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: responseBody,
          rawBody: data,
        });
      });
    });
    
    req.on('error', (error) => {
      reject(error);
    });
    
    if (options.body) {
      req.write(typeof options.body === 'string' ? options.body : JSON.stringify(options.body));
    }
    
    req.end();
  });
}

// WebSocket connection helper
function createWebSocketConnection(url, options = {}) {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(url);
    let resolved = false;
    
    const timeout = setTimeout(() => {
      if (!resolved) {
        resolved = true;
        ws.terminate();
        reject(new Error('WebSocket connection timeout'));
      }
    }, options.timeout || config.timeout);
    
    ws.on('open', () => {
      clearTimeout(timeout);
      if (!resolved) {
        resolved = true;
        resolve(ws);
      }
    });
    
    ws.on('error', (error) => {
      clearTimeout(timeout);
      if (!resolved) {
        resolved = true;
        reject(error);
      }
    });
  });
}

// Test Redis connectivity
async function testRedisConnectivity() {
  logger.section('Redis Connectivity Test');
  
  try {
    const redis = await initRedis();
    logger.debug('Sending PING to Redis...');
    const result = await redis.ping();
    
    if (result === 'PONG') {
      logger.result('Redis connectivity', true, 'Connection successful');
      
      // Test session key
      if (config.userId && config.userType) {
        const sessionKey = `session:${config.userId}:${config.userType}`;
        logger.debug(`Checking for session key: ${sessionKey}`);
        
        const sessionExists = await redis.exists(sessionKey);
        if (sessionExists) {
          const session = await redis.get(sessionKey);
          logger.result('Redis session', true, `Session exists for ${config.userId}`);
          logger.debug('Session data:', session);
        } else {
          logger.result('Redis session', false, `No session found for ${config.userId}`);
          logger.info('This is expected if you haven\'t authenticated yet');
        }
      }
      
      return true;
    } else {
      logger.result('Redis connectivity', false, `Unexpected response: ${result}`);
      return false;
    }
  } catch (error) {
    logger.result('Redis connectivity', false, error.message);
    logger.debug('Error details:', error);
    
    // Suggest solutions
    logger.warn('Possible solutions:');
    logger.warn('1. Ensure Redis is running on ' + config.redisHost + ':' + config.redisPort);
    logger.warn('2. Check Redis password if authentication is enabled');
    logger.warn('3. Verify network connectivity to Redis host');
    
    return false;
  }
}

// Test HTTP endpoints
async function testHttpEndpoints() {
  logger.section('HTTP API Test');
  
  const baseUrl = `http://localhost:${config.port}`;
  let allPassed = true;
  
  // Test health endpoint
  try {
    logger.debug(`Testing health endpoint: ${baseUrl}/health`);
    const healthResponse = await makeRequest(`${baseUrl}/health`);
    
    if (healthResponse.statusCode === 200) {
      logger.result('Health endpoint', true, 'Responded with 200 OK');
    } else {
      logger.result('Health endpoint', false, `Responded with ${healthResponse.statusCode}`);
      allPassed = false;
    }
    
    logger.debug('Health response:', healthResponse.body);
  } catch (error) {
    logger.result('Health endpoint', false, error.message);
    logger.debug('Error details:', error);
    allPassed = false;
    
    // Suggest solutions
    logger.warn('Possible solutions:');
    logger.warn(`1. Ensure server is running on port ${config.port}`);
    logger.warn('2. Check for any firewall or network issues');
    logger.warn('3. Verify the server process is started');
    
    // Try to check if process is running
    try {
      const checkProcess = promisify(exec);
      const { stdout } = await checkProcess(`lsof -i :${config.port}`);
      if (stdout.trim()) {
        logger.info('Process found listening on port ' + config.port + ':');
        logger.info(stdout.trim());
      } else {
        logger.warn('No process found listening on port ' + config.port);
      }
    } catch (e) {
      logger.warn('No process found listening on port ' + config.port);
    }
    
    return false;
  }
  
  // Test authentication if token is provided
  if (config.token) {
    try {
      logger.debug(`Testing authenticated endpoint: ${baseUrl}/api/chat/conversations`);
      const authResponse = await makeRequest(`${baseUrl}/api/chat/conversations`, {
        headers: {
          'Authorization': `Bearer ${config.token}`
        }
      });
      
      if (authResponse.statusCode === 200) {
        logger.result('Authentication', true, 'Successfully authenticated');
      } else if (authResponse.statusCode === 401) {
        logger.result('Authentication', false, 'Unauthorized - token may be invalid or expired');
        allPassed = false;
      } else {
        logger.result('Authentication', false, `Unexpected status code: ${authResponse.statusCode}`);
        allPassed = false;
      }
      
      logger.debug('Auth response:', authResponse.body);
    } catch (error) {
      logger.result('Authentication', false, error.message);
      logger.debug('Error details:', error);
      allPassed = false;
    }
  }
  
  return allPassed;
}

// Test WebSocket connectivity
async function testWebSocketConnectivity() {
  logger.section('WebSocket Connectivity Test');
  
  if (!config.token || !config.userId) {
    logger.warn('WebSocket test requires token and user-id parameters');
    return false;
  }
  
  const wsUrl = `ws://localhost:${config.port}/ws?token=${encodeURIComponent(config.token)}&userId=${encodeURIComponent(config.userId)}&userType=${config.userType}`;
  logger.debug(`Connecting to WebSocket: ${wsUrl.replace(/token=([^&]{1,10}).*?(&|$)/, 'token=$1...$2')}`);
  
  let ws;
  let connected = false;
  let authenticated = false;
  let joinedRoom = false;
  let messageSent = false;
  let messageReceived = false;
  
  try {
    ws = await createWebSocketConnection(wsUrl);
    connected = true;
    logger.result('WebSocket connection', true, 'Connection established');
    
    // Set up message handler
    const messagePromise = new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Timed out waiting for WebSocket messages'));
      }, config.timeout);
      
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          logger.debug('Received WebSocket message:', message);
          
          if (message.type === 'CONNECTION_SUCCESS' || message.type === 'connection') {
            authenticated = true;
            logger.result('WebSocket authentication', true, `Authenticated as ${message.userId} (${message.userType})`);
            
            // Generate a test conversation ID
            const testConversationId = 'test-' + createHash('md5').update(config.userId + Date.now()).digest('hex').substring(0, 8);
            
            // Join conversation
            logger.debug(`Joining test conversation: ${testConversationId}`);
            ws.send(JSON.stringify({
              type: 'JOIN_CONVERSATION',
              conversationId: testConversationId,
              timestamp: new Date().toISOString()
            }));
          }
          
          if (message.type === 'JOIN_CONFIRMATION' || message.type === 'user_joined') {
            joinedRoom = true;
            logger.result('WebSocket join room', true, `Joined conversation ${message.conversationId}`);
            
            // Send test message
            const testMessage = {
              type: 'TEXT_MESSAGE',
              conversationId: message.conversationId,
              content: `Test message from debug script at ${new Date().toISOString()}`,
              timestamp: new Date().toISOString()
            };
            
            logger.debug('Sending test message:', testMessage);
            ws.send(JSON.stringify(testMessage));
            messageSent = true;
          }
          
          if (message.type === 'TEXT_MESSAGE' || message.type === 'MESSAGE_DELIVERED') {
            messageReceived = true;
            logger.result('WebSocket message', true, 'Message sent and confirmation received');
            
            clearTimeout(timeout);
            resolve(true);
          }
        } catch (error) {
          logger.error('Error parsing WebSocket message:', error);
          logger.debug('Raw message:', data.toString());
        }
      });
    });
    
    // Wait for the complete message flow
    await messagePromise;
    
    return true;
  } catch (error) {
    logger.result('WebSocket connection', connected, connected ? 'Connected but error occurred' : 'Connection failed');
    logger.result('WebSocket authentication', authenticated, authenticated ? 'Authenticated but error occurred' : 'Authentication failed');
    logger.result('WebSocket join room', joinedRoom, joinedRoom ? 'Joined room but error occurred' : 'Failed to join room');
    logger.result('WebSocket message', messageSent && messageReceived, messageSent ? 'Message sent but no confirmation' : 'Failed to send message');
    
    logger.error('WebSocket error:', error.message);
    logger.debug('Error details:', error);
    
    // Suggest solutions
    logger.warn('Possible solutions:');
    logger.warn(`1. Ensure WebSocket server is running on ws://localhost:${config.port}/ws`);
    logger.warn('2. Check if token is valid and not expired');
    logger.warn('3. Verify Redis is running and accessible');
    logger.warn('4. Check server logs for authentication errors');
    
    return false;
  } finally {
    if (ws) {
      ws.close();
    }
  }
}

// Test token and authentication
async function testAuthentication() {
  logger.section('Authentication Test');
  
  if (!config.token) {
    logger.warn('Authentication test requires token parameter');
    return false;
  }
  
  try {
    // Decode token without verification
    const tokenParts = config.token.split('.');
    if (tokenParts.length !== 3) {
      logger.result('Token format', false, 'Invalid JWT format (should have 3 parts)');
      return false;
    }
    
    let payload;
    try {
      payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
      logger.result('Token decode', true, 'Successfully decoded token payload');
    } catch (e) {
      logger.result('Token decode', false, 'Failed to decode token payload');
      return false;
    }
    
    // Check token expiration
    const now = Math.floor(Date.now() / 1000);
    if (payload.exp && payload.exp < now) {
      logger.result('Token expiration', false, `Token expired at ${new Date(payload.exp * 1000).toISOString()}`);
      return false;
    } else if (payload.exp) {
      const expiresIn = payload.exp - now;
      const hours = Math.floor(expiresIn / 3600);
      const minutes = Math.floor((expiresIn % 3600) / 60);
      logger.result('Token expiration', true, `Token valid for ${hours}h ${minutes}m (expires at ${new Date(payload.exp * 1000).toISOString()})`);
    } else {
      logger.result('Token expiration', false, 'No expiration claim found in token');
    }
    
    // Check token issuer
    const customerIssuer = `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.CUSTOMER_COGNITO_USER_POOL_ID}`;
    const nurseIssuer = `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.NURSE_COGNITO_USER_POOL_ID}`;
    
    if (payload.iss === customerIssuer) {
      logger.result('Token issuer', true, 'Token issued by Customer/Patient Cognito User Pool');
      logger.debug('Expected user type: patient');
    } else if (payload.iss === nurseIssuer) {
      logger.result('Token issuer', true, 'Token issued by Nurse Cognito User Pool');
      logger.debug('Expected user type: nurse');
    } else {
      logger.result('Token issuer', false, `Unknown issuer: ${payload.iss}`);
      logger.debug('Expected issuers:');
      logger.debug(`- Customer: ${customerIssuer}`);
      logger.debug(`- Nurse: ${nurseIssuer}`);
    }
    
    // Check subject (user ID)
    if (payload.sub) {
      logger.result('Token subject', true, `Subject (user ID): ${payload.sub}`);
      
      if (config.userId && payload.sub !== config.userId) {
        logger.warn(`Token subject (${payload.sub}) doesn't match provided user-id (${config.userId})`);
      }
    } else {
      logger.result('Token subject', false, 'No subject claim found in token');
    }
    
    // Check audience
    const customerClientId = process.env.CUSTOMER_COGNITO_APP_CLIENT_ID;
    const nurseClientId = process.env.NURSE_COGNITO_APP_CLIENT_ID;
    
    if (payload.aud === customerClientId || payload.client_id === customerClientId) {
      logger.result('Token audience', true, 'Token audience is Customer/Patient App Client');
    } else if (payload.aud === nurseClientId || payload.client_id === nurseClientId) {
      logger.result('Token audience', true, 'Token audience is Nurse App Client');
    } else {
      logger.result('Token audience', false, `Unknown audience: ${payload.aud || payload.client_id}`);
      logger.debug('Expected audiences:');
      logger.debug(`- Customer: ${customerClientId}`);
      logger.debug(`- Nurse: ${nurseClientId}`);
    }
    
    // Display other useful claims
    logger.debug('Token payload:', payload);
    
    return true;
  } catch (error) {
    logger.error('Authentication test error:', error.message);
    logger.debug('Error details:', error);
    return false;
  }
}

// Check server environment
async function checkEnvironment() {
  logger.section('Environment Check');
  
  // Check Node.js version
  const nodeVersion = process.version;
  logger.info(`Node.js version: ${nodeVersion}`);
  
  // Check environment variables
  const requiredEnvVars = [
    'PORT',
    'AWS_REGION',
    'CUSTOMER_COGNITO_USER_POOL_ID',
    'CUSTOMER_COGNITO_APP_CLIENT_ID',
    'NURSE_COGNITO_USER_POOL_ID',
    'NURSE_COGNITO_APP_CLIENT_ID',
    'REDIS_HOST',
    'REDIS_PORT'
  ];
  
  let allEnvVarsPresent = true;
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      logger.result(`Environment variable ${envVar}`, true, 'Present');
    } else {
      logger.result(`Environment variable ${envVar}`, false, 'Missing');
      allEnvVarsPresent = false;
    }
  }
  
  // Check if server is running
  try {
    const response = await makeRequest(`http://localhost:${config.port}/health`, { timeout: 2000 });
    logger.result('Server running', true, `Server is running on port ${config.port}`);
  } catch (error) {
    logger.result('Server running', false, `Server is not running on port ${config.port}`);
  }
  
  return allEnvVarsPresent;
}

// Run all tests
async function runTests() {
  logger.info(`Starting debug tests for chat-room-api on port ${config.port}`);
  logger.info(`Date: ${new Date().toISOString()}`);
  
  const results = {};
  
  try {
    // Always check environment
    results.environment = await checkEnvironment();
    
    // Run selected tests
    if (config.testOnly === 'all' || config.testOnly === 'redis') {
      results.redis = await testRedisConnectivity();
    }
    
    if (config.testOnly === 'all' || config.testOnly === 'http') {
      results.http = await testHttpEndpoints();
    }
    
    if (config.testOnly === 'all' || config.testOnly === 'auth') {
      results.auth = await testAuthentication();
    }
    
    if (config.testOnly === 'all' || config.testOnly === 'ws') {
      results.ws = await testWebSocketConnectivity();
    }
    
    // Print summary
    logger.section('Test Summary');
    
    let allPassed = true;
    for (const [test, passed] of Object.entries(results)) {
      const status = passed ? `${colors.green}PASS${colors.reset}` : `${colors.red}FAIL${colors.reset}`;
      logger.info(`${test.padEnd(12)}: ${status}`);
      if (!passed) allPassed = false;
    }
    
    if (allPassed) {
      logger.success('\nAll tests passed! The server appears to be functioning correctly.');
    } else {
      logger.warn('\nSome tests failed. Review the output above for details and suggested solutions.');
    }
    
    // Close Redis connection if opened
    if (redisClient && redisClient.isOpen) {
      await redisClient.quit();
    }
    
    return allPassed;
  } catch (error) {
    logger.error('Error running tests:', error);
    
    // Close Redis connection if opened
    if (redisClient && redisClient.isOpen) {
      await redisClient.quit();
    }
    
    return false;
  }
}

// Run the tests
runTests()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    logger.error('Fatal error:', error);
    process.exit(1);
  });
