# Authentication Guide  
Nurserv **Chat-Room API** (Node/Express, MongoDB, Redis, WebSocket)

This document explains the new AWS Cognito-based, multi-user-pool authentication layer that protects every REST and WebSocket endpoint.

---

## 1 · Overview

```
┌────────────┐        1  idToken / accessToken (JWT)
│  Frontend  │──────────────────────────────────────────┐
└────────────┘                                          │
        │                                               │
        │2  Authorization: Bearer <JWT>                 ▼
        │                                       ┌─────────────────┐
        │                                       │  chat-room-api  │
        │                                       └─────────────────┘
        │                                               │
        │3  JWKS fetch & verify                         ▼
        │                                       ┌─────────────────┐
        │                                       │ Cognito JWKS URI│
        │                                       └─────────────────┘
        │                                               │
        │4  store session (Redis)                       ▼
        │                                       ┌─────────────────┐
        │                                       │     Redis       │
        │                                       └─────────────────┘
```

1. Front-end obtains **idToken** (JWT) from Cognito after login.  
2. Token is added to `Authorization` header for REST and to WS query string.  
3. Middleware validates token signature & claims against **two** Cognito user pools (Patient & Nurse) using JWKS.  
4. If valid, a short-lived session key is cached in Redis (for revocation & TTL).

---

## 2 · Environment Variables

| Key | Description | Example |
|-----|-------------|---------|
| `AWS_REGION` | Region hosting both user pools | `ap-south-1` |
| `CUSTOMER_COGNITO_USER_POOL_ID` | Patient pool ID | `ap-south-1_vHvB3OVv7` |
| `CUSTOMER_COGNITO_APP_CLIENT_ID` | Patient app client ID | `712qmho9…` |
| `NURSE_COGNITO_USER_POOL_ID` | Nurse pool ID | `ap-south-1_Yx9Ck9Ywl` |
| `NURSE_COGNITO_APP_CLIENT_ID` | Nurse app client ID | `606aloss…` |
| `USE_TEST_AUTH` | `true` enables local **test tokens** (see §5) | `false` |
| `JWT_TEST_SECRET` | HMAC secret for test tokens | `test-secret` |
| `REDIS_HOST/PORT/PASSWORD` | Session cache | &nbsp; |

Place them in `.env.production` or `.env.development`.

---

## 3 · Token Verification Logic

1. **Extract** token from `Authorization: Bearer <token>`.
2. **DEV shortcut:** if `USE_TEST_AUTH=true` → verify with `JWT_TEST_SECRET`.
3. **Decode (no verify)** to read `kid` & `iss`.
4. **Select pool:**  
   • If `iss` matches a pool → try that pool first, otherwise try both.  
5. **Fetch signing key** via JWKS (cached & rate-limited).  
6. **Verify** (`RS256`, issuer, audience).  
7. **Create `req.user`:** `{ id, type(patient|nurse), email, username … }`.
8. **Cache session** in Redis: `session:<userId>:<type>` TTL 30 min.

---

## 4 · Using the Middleware

```js
const {
  authenticateUser,
  requirePatient,
  requireNurse,
  requirePatientOrNurse
} = require('./middleware/auth');

router.post(
  '/conversations',
  authenticateUser,
  requirePatient,          // only patients create conversations
  controller.createConversation
);

router.get(
  '/conversations/:conversationId/messages',
  authenticateUser,
  requirePatientOrNurse,   // either participant can fetch
  validateConversationAccess,
  controller.listMessages
);
```

### WebSocket

The WS URL must include the token, e.g.:

`wss://api.nurserv.com/ws?token=<JWT>&userId=<sub>&userType=patient`

`useWebSocket.ts` already handles this.

---

## 5 · Local Development with Test Tokens

When front-end and API run locally you may not have Cognito credentials.

1. Set  
   ```
   NODE_ENV=development
   USE_TEST_AUTH=true
   JWT_TEST_SECRET=test-secret
   ```
2. Generate token:

```bash
node test/generate-test-tokens.js --userType patient --sub 123
```

3. Use the printed token in Postman / your browser.

⚠️ **Never** enable `USE_TEST_AUTH` in production.

---

## 6 · Testing

### 6.1 Health Check

```bash
curl http://localhost:8080/health
```

### 6.2 REST

```bash
TOKEN=<valid JWT>

curl -H "Authorization: Bearer $TOKEN" \
     http://localhost:8080/api/chat/conversations
```

• `401` → invalid/expired token  
• `403` → role or conversation access violation

### 6.3 WebSocket

```bash
wscat -c "ws://localhost:8080/ws?token=$TOKEN&userId=123&userType=nurse"
```

Send:

```json
{"type":"JOIN_CONVERSATION","conversationId":"abc"}
```

Expect server response `{ "type":"CONNECTION_SUCCESS" }`.

---

## 7 · Troubleshooting

| Symptom | Checklist |
|---------|-----------|
| **401 Unauthorized** | Token expired? Wrong pool IDs? Check `iss`, `aud`. |
| **Token verification failed with all user pools** | Ensure env vars match Cognito pool IDs & client IDs. |
| **Redis session expired** | Default TTL 30 min. Front-end should refresh (re-login) or increase TTL. |
| **WebSocket disconnects** | Confirm token in querystring, network proxies, or ping/heartbeat timeouts. |
| **`USE_TEST_AUTH` ignored** | `NODE_ENV` must be `development`. |

Enable verbose auth logs:

```bash
DEBUG=chat:auth,* npm run dev
```

---

## 8 · Security Best Practices

1. Always serve API & Cognito over **HTTPS**.  
2. Rotate `JWT_SECRET`, `SESSION_SECRET`, Redis password via secret manager.  
3. Enforce **short token TTL** (≤ 1 h) in Cognito.  
4. Monitor `/metrics` (`prom-client`) for auth failures.  
5. Disable `USE_TEST_AUTH` outside local dev.

---

## 9 · References

* AWS Cognito JWKS: `https://cognito-idp.<region>.amazonaws.com/<poolId>/.well-known/jwks.json`
* RFC 7519 – JSON Web Token (JWT)
* [Nurserv Chat-Room API repository](https://github.com/VITCS/chat-room-api)
