# Server Configuration
PORT=8080
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/chat_room_db
MONGODB_OPTIONS={"useNewUrlParser":true,"useUnifiedTopology":true}

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload (for future use)
MAX_FILE_SIZE=10485760
UPLOAD_PATH=./uploads

# AWS/Wasabi Configuration (for file storage)
WASABI_ACCESS_KEY_ID=your_wasabi_access_key
WASABI_SECRET_ACCESS_KEY=your_wasabi_secret_key
WASABI_ENDPOINT=s3.us-east-1.wasabisys.com
WASABI_REGION=us-east-1
WASABI_BUCKET_NAME=your_bucket_name

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Security
CORS_ORIGIN=http://localhost:3000
SESSION_SECRET=your_session_secret_here 