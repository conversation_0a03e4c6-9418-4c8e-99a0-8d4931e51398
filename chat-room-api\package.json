{"name": "chat-room-api", "version": "1.0.0", "description": "", "homepage": "https://github.com/VITCS/chat-room-api#readme", "bugs": {"url": "https://github.com/VITCS/chat-room-api/issues"}, "repository": {"type": "git", "url": "git+https://github.com/VITCS/chat-room-api.git"}, "license": "ISC", "author": "<PERSON><PERSON><PERSON>", "type": "commonjs", "main": "src/server.js", "scripts": {"start": "nodemon src/server.js", "dev": "nodemon src/server.js", "start:prod": "NODE_ENV=production node src/server.js", "test": "jest", "test:api": "node test/api-test.js", "test:comprehensive": "node test/comprehensive-test.js", "test:websocket": "node test/websocket-test.js", "test:bidirectional": "node test/bidirectional-messaging-test.js", "test:conversation-retrieval": "node test/conversation-retrieval-test.js", "test:generate-tokens": "node test/generate-test-tokens.js", "test:quick": "node test/quick-test.js", "test:all": "npm run test:generate-tokens && npm run test:comprehensive && npm run test:websocket && npm run test:bidirectional && npm run test:conversation-retrieval", "init:db": "node database/init-mongodb.js"}, "dependencies": {"aws-sdk": "^2.1692.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^2.0.1", "mongodb": "^6.3.0", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "redis": "^4.6.11", "uuid": "^11.1.0", "ws": "^8.18.3"}, "devDependencies": {"axios": "^1.6.0", "jest": "^29.7.0", "nodemon": "^3.1.9"}}