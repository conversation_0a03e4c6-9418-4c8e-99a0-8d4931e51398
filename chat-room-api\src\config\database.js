const mongoose = require('mongoose');
require('dotenv').config();

const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/chat_room_db';


// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(mongoUri, {useNewUrlParser:true,useUnifiedTopology:true});
    console.log('MongoDB connected successfully');
    
    // Handle connection events
    mongoose.connection.on('error', (err) => {
      console.error('MongoDB connection error:', err);
    });
    
    mongoose.connection.on('disconnected', () => {
      console.log('MongoDB disconnected');
    });
    
    // Graceful shutdown
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      console.log('MongoDB connection closed through app termination');
      process.exit(0);
    });
    
  } catch (error) {
    console.error('MongoDB connection failed:', error);
    process.exit(1);
  }
};

// Test database connection
const testConnection = async () => {
  try {
    await connectDB();
    console.log('Database connection test successful');
  } catch (error) {
    console.error('Database connection test failed:', error);
    process.exit(1);
  }
};

module.exports = { connectDB, testConnection, mongoose }; 