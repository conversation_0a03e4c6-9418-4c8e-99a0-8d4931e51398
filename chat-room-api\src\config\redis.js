const redis = require('redis');
require('dotenv').config();

const redisConfig = {
  socket: {
    host: process.env.REDIS_HOST || 'localhost',
    port: process.env.REDIS_PORT || 6379,
  }
};

if (process.env.REDIS_PASSWORD) {
  redisConfig.password = process.env.REDIS_PASSWORD;
}

const client = redis.createClient(redisConfig);

client.on('connect', () => {
  console.log('Redis client connected');
});
client.on('error', (err) => {
  console.error('Redis Client Error:', err);
});
client.on('ready', () => {
  console.log('Redis client ready');
});
client.on('end', () => {
  console.log('Redis client disconnected');
});

module.exports = { client }; 