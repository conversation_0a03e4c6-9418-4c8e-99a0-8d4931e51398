/**
 * Authentication Middleware
 * -------------------------
 * Replaces the basic JWT verification with the multi-pool Cognito/JWKS strategy
 * used by the nurserv-c-api.  Supports:
 *   • Customer & Nurse user pools
 *   • JW<PERSON> key fetching / caching
 *   • Development mode test tokens
 *   • Redis-backed session validation
 */

const { verifyAndGetUser, validateAndRefreshSession } = require('../utils/authUtils');
const { Conversation } = require('../models');

// Middleware to authenticate user
const authenticateUser = async (req, res, next) => {
  try {
    /* -------------------------------------------------------------------- */
    /*                        EXTRACT & VALIDATE TOKEN                       */
    /* -------------------------------------------------------------------- */
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ success: false, message: 'No authorization header' });
    }

    const token = authHeader.split(' ')[1];
    if (!token) {
      return res.status(401).json({ success: false, message: 'No token provided' });
    }

    /* -------------------------- COGNITO VERIFICATION --------------------- */
    const { verifiedToken, userType, userId, email, username, tokenUse, clientId } = await verifyAndGetUser(token);

    /* ---------------------------- REDIS SESSION -------------------------- */
    const sessionValid = await validateAndRefreshSession(userId, userType);
    
    if (!sessionValid) {
      return res.status(401).json({
        success: false,
        message: 'Session expired. Please login again.'
      });
    }

    /* -------------------------- SET REQ.USER OBJ ------------------------- */
    req.user = {
      id: userId,
      type: userType,
      email: email,
      username: username,
      tokenUse: tokenUse,
      clientId: clientId
    };

    return next();
  } catch (error) {
    console.error('Authentication error:', error);
    return res.status(500).json({ success: false, message: 'Authentication failed' });
  }
};

// Middleware to check if user is a patient
const requirePatient = (req, res, next) => {
  if (req.user.type !== 'patient') {
    return res.status(403).json({ 
      success: false, 
      message: 'Access denied. Patient role required.' 
    });
  }
  next();
};

// Middleware to check if user is a nurse
const requireNurse = (req, res, next) => {
  if (req.user.type !== 'nurse') {
    return res.status(403).json({ 
      success: false, 
      message: 'Access denied. Nurse role required.' 
    });
  }
  next();
};

// Middleware to check if user is either patient or nurse
const requirePatientOrNurse = (req, res, next) => {
  if (req.user.type !== 'patient' && req.user.type !== 'nurse') {
    return res.status(403).json({ 
      success: false, 
      message: 'Access denied. Patient or nurse role required.' 
    });
  }
  next();
};

// Middleware to validate conversation access
const validateConversationAccess = async (req, res, next) => {
  try {
    const { conversationId } = req.params;
    
    const conversation = await Conversation.findById(conversationId).lean();
    
    if (!conversation) {
      return res.status(404).json({ 
        success: false, 
        message: 'Conversation not found' 
      });
    }
    
    // Check if user has access to this conversation
    if (req.user.type === 'patient' && conversation.customerId !== req.user.id) {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied to this conversation' 
      });
    }
    
    if (req.user.type === 'nurse' && conversation.nurseId !== req.user.id) {
      return res.status(403).json({ 
        success: false, 
        message: 'Access denied to this conversation' 
      });
    }
    
    // Check if conversation is active
    if (conversation.status === 'inactive') {
      return res.status(400).json({ 
        success: false, 
        message: 'Conversation is inactive' 
      });
    }
    
    req.conversation = conversation;
    next();
  } catch (error) {
    console.error('Conversation access validation error:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Error validating conversation access' 
    });
  }
};

module.exports = {
  authenticateUser,
  requirePatient,
  requireNurse,
  requirePatientOrNurse,
  validateConversationAccess
}; 