/**
 * Monitoring Middleware
 * 
 * Comprehensive monitoring middleware for the chat room API including:
 * - Prometheus metrics collection
 * - Request performance tracking
 * - Structured logging with correlation IDs
 * - Error monitoring
 * - Health checks for all components
 */

const { createLogger, format, transports } = require('winston');
const { v4: uuidv4 } = require('uuid');
const client = require('prom-client');
const os = require('os');
const { client: redisClient } = require('../config/redis');
const { mongoose } = require('../config/database');

// Initialize Prometheus metrics collection
const collectDefaultMetrics = client.collectDefaultMetrics;
const Registry = client.Registry;
const register = new Registry();

// Enable default metrics collection (CPU, memory, etc.)
collectDefaultMetrics({ register });

// Define custom metrics
const httpRequestDurationMicroseconds = new client.Histogram({
  name: 'http_request_duration_ms',
  help: 'Duration of HTTP requests in ms',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [1, 5, 15, 50, 100, 200, 500, 1000, 2000, 5000, 10000]
});

const httpRequestCounter = new client.Counter({
  name: 'http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const errorCounter = new client.Counter({
  name: 'errors_total',
  help: 'Total number of errors',
  labelNames: ['type', 'code', 'message']
});

const wsConnectionsGauge = new client.Gauge({
  name: 'websocket_connections_current',
  help: 'Current number of WebSocket connections'
});

const wsMessagesCounter = new client.Counter({
  name: 'websocket_messages_total',
  help: 'Total number of WebSocket messages',
  labelNames: ['type', 'direction']
});

const cacheHitCounter = new client.Counter({
  name: 'cache_hits_total',
  help: 'Total number of cache hits',
  labelNames: ['cache_type']
});

const cacheMissCounter = new client.Counter({
  name: 'cache_misses_total',
  help: 'Total number of cache misses',
  labelNames: ['cache_type']
});

const dbOperationDurationHistogram = new client.Histogram({
  name: 'db_operation_duration_ms',
  help: 'Duration of database operations in ms',
  labelNames: ['operation', 'model'],
  buckets: [1, 5, 15, 50, 100, 200, 500, 1000, 2000, 5000]
});

// Register custom metrics
register.registerMetric(httpRequestDurationMicroseconds);
register.registerMetric(httpRequestCounter);
register.registerMetric(errorCounter);
register.registerMetric(wsConnectionsGauge);
register.registerMetric(wsMessagesCounter);
register.registerMetric(cacheHitCounter);
register.registerMetric(cacheMissCounter);
register.registerMetric(dbOperationDurationHistogram);

// Create Winston logger with structured logging
const logger = createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: format.combine(
    format.timestamp(),
    process.env.LOG_FORMAT === 'json' 
      ? format.json()
      : format.printf(({ timestamp, level, message, ...rest }) => {
          return `${timestamp} ${level}: ${message} ${JSON.stringify(rest)}`;
        })
  ),
  defaultMeta: { service: 'chat-room-api' },
  transports: [
    new transports.Console(),
    process.env.LOG_FILE 
      ? new transports.File({ filename: process.env.LOG_FILE })
      : null
  ].filter(Boolean)
});

/**
 * Middleware to add request ID and start timer
 */
const requestTracker = (req, res, next) => {
  // Generate unique request ID or use existing one from headers
  req.id = req.headers['x-request-id'] || uuidv4();
  res.setHeader('x-request-id', req.id);
  
  // Add correlation ID if provided (for tracing across services)
  req.correlationId = req.headers['x-correlation-id'] || req.id;
  res.setHeader('x-correlation-id', req.correlationId);
  
  // Start timer for request duration
  req.startTime = Date.now();
  
  // Capture original end function to intercept it
  const originalEnd = res.end;
  
  // Override end function to calculate duration and log request
  res.end = function(chunk, encoding) {
    // Calculate request duration
    const duration = Date.now() - req.startTime;
    
    // Get route pattern if available (from Express)
    const route = req.route ? req.route.path : req.path;
    
    // Increment request counter
    httpRequestCounter.labels(req.method, route, res.statusCode).inc();
    
    // Record request duration
    httpRequestDurationMicroseconds.labels(req.method, route, res.statusCode).observe(duration);
    
    // Log request details
    logger.info('Request completed', {
      requestId: req.id,
      correlationId: req.correlationId,
      method: req.method,
      url: req.originalUrl,
      route: route,
      statusCode: res.statusCode,
      duration: duration,
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      userId: req.user?.id || 'unauthenticated'
    });
    
    // Call original end function
    return originalEnd.call(this, chunk, encoding);
  };
  
  // Log incoming request
  logger.debug('Request received', {
    requestId: req.id,
    correlationId: req.correlationId,
    method: req.method,
    url: req.originalUrl,
    userAgent: req.headers['user-agent'],
    ip: req.ip
  });
  
  next();
};

/**
 * Error logging middleware
 */
const errorLogger = (err, req, res, next) => {
  // Increment error counter
  errorCounter.labels(err.name || 'unknown', err.code || 'unknown', err.message || 'unknown').inc();
  
  // Log error with request details
  logger.error('Request error', {
    requestId: req.id,
    correlationId: req.correlationId,
    method: req.method,
    url: req.originalUrl,
    error: {
      name: err.name,
      message: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined,
      code: err.code
    },
    userId: req.user?.id || 'unauthenticated'
  });
  
  next(err);
};

/**
 * Middleware to expose Prometheus metrics endpoint
 */
const metricsMiddleware = async (req, res) => {
  try {
    res.set('Content-Type', register.contentType);
    res.end(await register.metrics());
  } catch (error) {
    logger.error('Error generating metrics', { error });
    res.status(500).end();
  }
};

/**
 * Health check middleware with detailed component status
 */
const healthCheck = async (req, res) => {
  const startTime = Date.now();
  const checks = {};
  let overallStatus = 'ok';
  
  // Check MongoDB connection
  try {
    checks.mongodb = {
      status: mongoose.connection.readyState === 1 ? 'ok' : 'error',
      details: {
        readyState: mongoose.connection.readyState,
        host: mongoose.connection.host,
        name: mongoose.connection.name
      }
    };
    
    if (checks.mongodb.status !== 'ok') {
      overallStatus = 'error';
    }
  } catch (error) {
    checks.mongodb = {
      status: 'error',
      error: error.message
    };
    overallStatus = 'error';
  }
  
  // Check Redis connection
  try {
    const pingResult = await redisClient.ping();
    checks.redis = {
      status: pingResult === 'PONG' ? 'ok' : 'error',
      details: {
        connected: redisClient.isOpen
      }
    };
    
    if (checks.redis.status !== 'ok') {
      overallStatus = 'error';
    }
  } catch (error) {
    checks.redis = {
      status: 'error',
      error: error.message
    };
    overallStatus = 'error';
  }
  
  // Check system resources
  checks.system = {
    status: 'ok',
    details: {
      uptime: process.uptime(),
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usagePercent: ((os.totalmem() - os.freemem()) / os.totalmem() * 100).toFixed(2)
      },
      cpu: {
        loadAvg: os.loadavg(),
        cpus: os.cpus().length
      }
    }
  };
  
  // Memory usage warning threshold (90%)
  if (((os.totalmem() - os.freemem()) / os.totalmem()) > 0.9) {
    checks.system.status = 'warning';
    if (overallStatus === 'ok') {
      overallStatus = 'warning';
    }
  }
  
  // Add WebSocket status if available
  if (global.wsManager) {
    try {
      const connectionCount = global.wsManager.getConnectionCount();
      checks.websocket = {
        status: 'ok',
        details: {
          connections: connectionCount,
          rooms: global.wsManager.getRoomCount()
        }
      };
    } catch (error) {
      checks.websocket = {
        status: 'error',
        error: error.message
      };
      overallStatus = 'error';
    }
  }
  
  // Calculate response time
  const responseTime = Date.now() - startTime;
  
  // Prepare response
  const healthResponse = {
    status: overallStatus,
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    checks,
    responseTime
  };
  
  // Set appropriate status code
  const statusCode = overallStatus === 'ok' ? 200 : overallStatus === 'warning' ? 200 : 503;
  
  // Log health check result if not ok
  if (overallStatus !== 'ok') {
    logger.warn('Health check issues detected', { healthResponse });
  }
  
  res.status(statusCode).json(healthResponse);
};

/**
 * Detailed health check for Kubernetes liveness/readiness probes
 */
const livenessProbe = async (req, res) => {
  try {
    // Basic check that the server is running and responsive
    res.status(200).json({
      status: 'ok',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message
    });
  }
};

/**
 * Readiness probe for Kubernetes
 */
const readinessProbe = async (req, res) => {
  try {
    // Check if MongoDB and Redis are connected
    const isMongoConnected = mongoose.connection.readyState === 1;
    const isRedisConnected = redisClient.isOpen;
    
    if (isMongoConnected && isRedisConnected) {
      res.status(200).json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        connections: {
          mongodb: 'connected',
          redis: 'connected'
        }
      });
    } else {
      res.status(503).json({
        status: 'error',
        timestamp: new Date().toISOString(),
        connections: {
          mongodb: isMongoConnected ? 'connected' : 'disconnected',
          redis: isRedisConnected ? 'connected' : 'disconnected'
        }
      });
    }
  } catch (error) {
    res.status(500).json({
      status: 'error',
      error: error.message
    });
  }
};

/**
 * Middleware to track WebSocket metrics
 * Should be called from the WebSocket manager
 */
const trackWebSocketConnection = (isConnected) => {
  if (isConnected) {
    wsConnectionsGauge.inc();
  } else {
    wsConnectionsGauge.dec();
  }
};

/**
 * Track WebSocket message
 */
const trackWebSocketMessage = (type, direction) => {
  wsMessagesCounter.labels(type, direction).inc();
};

/**
 * Track cache operations
 */
const trackCacheOperation = (isHit, cacheType) => {
  if (isHit) {
    cacheHitCounter.labels(cacheType).inc();
  } else {
    cacheMissCounter.labels(cacheType).inc();
  }
};

/**
 * Track database operation duration
 */
const trackDbOperation = (operation, model, duration) => {
  dbOperationDurationHistogram.labels(operation, model).observe(duration);
};

/**
 * Setup route for monitoring endpoints
 * @param {Express} app - Express application
 */
const setupMonitoringRoutes = (app) => {
  // Only expose metrics in non-development environments or if explicitly enabled
  if (process.env.NODE_ENV !== 'development' || process.env.ENABLE_METRICS === 'true') {
    app.get('/metrics', metricsMiddleware);
  }
  
  // Health check endpoints
  app.get('/health', healthCheck);
  app.get('/health/liveness', livenessProbe);
  app.get('/health/readiness', readinessProbe);
};

// Export the monitoring middleware and utilities
module.exports = {
  // Middleware
  requestTracker,
  errorLogger,
  metricsMiddleware,
  healthCheck,
  livenessProbe,
  readinessProbe,
  setupMonitoringRoutes,
  
  // Tracking functions
  trackWebSocketConnection,
  trackWebSocketMessage,
  trackCacheOperation,
  trackDbOperation,
  
  // Metrics registry for custom metrics
  register,
  
  // Logger for structured logging
  logger
};
