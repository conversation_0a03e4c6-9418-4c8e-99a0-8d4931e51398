/**
 * Security Middleware
 * 
 * Comprehensive security middleware for the chat room API including:
 * - Input sanitization
 * - XSS protection
 * - NoSQL injection prevention
 * - Request validation
 * - IP blocking
 * - Audit logging for suspicious activities
 */

const { body, param, query, validationResult } = require('express-validator');
const { client: redisClient } = require('../config/redis');
const { AuditLog } = require('../models');
const { v4: uuidv4 } = require('uuid');

// List of common XSS attack patterns
const XSS_PATTERNS = [
  /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
  /javascript:/gi,
  /onerror=/gi,
  /onload=/gi,
  /onclick=/gi,
  /onmouseover=/gi
];

// List of common NoSQL injection patterns
const NOSQL_INJECTION_PATTERNS = [
  /\$where/i,
  /\$ne/i,
  /\$gt/i,
  /\$lt/i,
  /\$exists/i,
  /\$regex/i,
  /".*\$.*":/i,
  /'.*\$.*':/i
];

// IP Blocklist TTL in seconds (24 hours)
const IP_BLOCKLIST_TTL = 86400;

// Suspicious patterns in request content
const SUSPICIOUS_PATTERNS = [
  /eval\(/gi,
  /execCommand/gi,
  /fromCharCode/gi,
  /document\.cookie/gi,
  /window\.location/gi,
  /\.innerHTML/gi,
  /\.outerHTML/gi,
  /fetch\(/gi,
  /XMLHttpRequest/gi
];

/**
 * Sanitize input by removing potentially dangerous patterns
 * @param {string} input - The input string to sanitize
 * @returns {string} - Sanitized string
 */
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  
  // Replace dangerous HTML entities
  let sanitized = input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;');
  
  // Remove XSS patterns
  XSS_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  return sanitized;
};

/**
 * Check for NoSQL injection patterns
 * @param {string} input - The input to check
 * @returns {boolean} - True if suspicious patterns found
 */
const hasNoSQLInjection = (input) => {
  if (typeof input !== 'string') return false;
  
  return NOSQL_INJECTION_PATTERNS.some(pattern => pattern.test(input));
};

/**
 * Check if request has suspicious content
 * @param {Object} req - Express request object
 * @returns {boolean} - True if suspicious content found
 */
const hasSuspiciousContent = (req) => {
  // Convert request body, query and params to strings for inspection
  const bodyStr = JSON.stringify(req.body || {});
  const queryStr = JSON.stringify(req.query || {});
  const paramsStr = JSON.stringify(req.params || {});
  const combinedStr = `${bodyStr}${queryStr}${paramsStr}`;
  
  // Check for suspicious patterns
  return SUSPICIOUS_PATTERNS.some(pattern => pattern.test(combinedStr)) || 
         NOSQL_INJECTION_PATTERNS.some(pattern => pattern.test(combinedStr));
};

/**
 * Log security audit event
 * @param {Object} req - Express request object
 * @param {string} eventType - Type of security event
 * @param {string} details - Additional details
 * @param {string} severity - Event severity (low, medium, high)
 */
const logSecurityEvent = async (req, eventType, details, severity = 'medium') => {
  try {
    const userId = req.user?.id || 'unauthenticated';
    const userType = req.user?.type || 'unknown';
    
    await AuditLog.create({
      eventType: `security:${eventType}`,
      userId,
      userType,
      resourceType: 'security',
      resourceId: uuidv4(),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'] || 'unknown',
      details: {
        path: req.path,
        method: req.method,
        query: req.query,
        params: req.params,
        body: req.body,
        additionalDetails: details,
        severity
      },
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
};

/**
 * Add IP to blocklist
 * @param {string} ip - IP address to block
 * @param {string} reason - Reason for blocking
 */
const blockIP = async (ip, reason) => {
  try {
    const key = `blocklist:ip:${ip}`;
    await redisClient.set(key, reason);
    await redisClient.expire(key, IP_BLOCKLIST_TTL);
    
    console.warn(`IP ${ip} blocked for ${reason}`);
  } catch (error) {
    console.error('Failed to block IP:', error);
  }
};

/**
 * Check if IP is in blocklist
 * @param {string} ip - IP address to check
 * @returns {Promise<boolean>} - True if IP is blocked
 */
const isIPBlocked = async (ip) => {
  try {
    const key = `blocklist:ip:${ip}`;
    return await redisClient.exists(key);
  } catch (error) {
    console.error('Failed to check IP blocklist:', error);
    return false;
  }
};

/**
 * Middleware to check if IP is blocked
 */
const ipBlocklistCheck = async (req, res, next) => {
  try {
    const ip = req.ip;
    
    if (await isIPBlocked(ip)) {
      await logSecurityEvent(req, 'blocked_ip_request', 'Request from blocked IP', 'high');
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }
    
    next();
  } catch (error) {
    console.error('IP blocklist check error:', error);
    next();
  }
};

/**
 * Middleware to sanitize request body, query and params
 */
const sanitizeRequest = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body) {
      Object.keys(req.body).forEach(key => {
        if (typeof req.body[key] === 'string') {
          req.body[key] = sanitizeInput(req.body[key]);
        }
      });
    }
    
    // Sanitize request query
    if (req.query) {
      Object.keys(req.query).forEach(key => {
        if (typeof req.query[key] === 'string') {
          req.query[key] = sanitizeInput(req.query[key]);
        }
      });
    }
    
    // Sanitize request params
    if (req.params) {
      Object.keys(req.params).forEach(key => {
        if (typeof req.params[key] === 'string') {
          req.params[key] = sanitizeInput(req.params[key]);
        }
      });
    }
    
    next();
  } catch (error) {
    console.error('Request sanitization error:', error);
    next();
  }
};

/**
 * Middleware to detect and handle suspicious requests
 */
const detectSuspiciousActivity = async (req, res, next) => {
  try {
    if (hasSuspiciousContent(req)) {
      const ip = req.ip;
      const userId = req.user?.id || 'unauthenticated';
      
      // Log the suspicious activity
      await logSecurityEvent(
        req, 
        'suspicious_request', 
        'Request contains potentially malicious patterns', 
        'high'
      );
      
      // Increment suspicious activity counter for this IP
      const key = `security:suspicious:${ip}`;
      const count = await redisClient.incr(key);
      await redisClient.expire(key, 3600); // Expire after 1 hour
      
      // If more than 3 suspicious activities from this IP in the last hour, block it
      if (count >= 3) {
        await blockIP(ip, 'Multiple suspicious requests');
        return res.status(403).json({
          success: false,
          message: 'Access denied due to suspicious activity'
        });
      }
      
      // Allow the request to proceed but with a warning
      console.warn(`Suspicious request detected from IP ${ip}, user ${userId}`);
    }
    
    next();
  } catch (error) {
    console.error('Suspicious activity detection error:', error);
    next();
  }
};

/**
 * Middleware to validate request data using express-validator
 * @param {Array} validations - Array of express-validator validations
 */
const validate = (validations) => {
  return async (req, res, next) => {
    // Execute all validations
    await Promise.all(validations.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }
    
    // Log validation failures that might indicate an attack
    if (errors.array().some(err => err.msg.includes('injection') || err.msg.includes('suspicious'))) {
      await logSecurityEvent(
        req,
        'validation_failure',
        `Validation failed: ${JSON.stringify(errors.array())}`,
        'medium'
      );
    }
    
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: errors.array()
    });
  };
};

/**
 * Common validation chains for different types of inputs
 */
const validations = {
  // User ID validation
  userId: () => param('userId')
    .isString()
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('User ID must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('User ID contains invalid characters'),
  
  // Conversation ID validation
  conversationId: () => param('conversationId')
    .isString()
    .trim()
    .isLength({ min: 3, max: 50 })
    .withMessage('Conversation ID must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Conversation ID contains invalid characters'),
  
  // Message content validation
  messageContent: () => body('content')
    .isString()
    .trim()
    .isLength({ min: 1, max: 5000 })
    .withMessage('Message content must be between 1 and 5000 characters')
    .custom(value => {
      if (XSS_PATTERNS.some(pattern => pattern.test(value))) {
        throw new Error('Message contains suspicious XSS patterns');
      }
      if (hasNoSQLInjection(value)) {
        throw new Error('Message contains suspicious injection patterns');
      }
      return true;
    }),
  
  // Pagination validation
  pagination: () => [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Page must be a positive integer'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('Limit must be between 1 and 100')
  ],
  
  // Search query validation
  searchQuery: () => query('q')
    .isString()
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be between 1 and 100 characters')
    .custom(value => {
      if (XSS_PATTERNS.some(pattern => pattern.test(value))) {
        throw new Error('Search query contains suspicious XSS patterns');
      }
      if (hasNoSQLInjection(value)) {
        throw new Error('Search query contains suspicious injection patterns');
      }
      return true;
    })
};

// Security headers middleware (extends helmet)
const securityHeaders = (req, res, next) => {
  // Add additional security headers not covered by helmet
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'camera=(), microphone=(), geolocation=()');
  
  // Set strict Content Security Policy in production
  if (process.env.NODE_ENV === 'production') {
    res.setHeader(
      'Content-Security-Policy',
      "default-src 'self'; script-src 'self'; connect-src 'self'; img-src 'self' data:; style-src 'self'; font-src 'self'; object-src 'none'; frame-ancestors 'none';"
    );
  }
  
  next();
};

// Combine all security middleware into a single function
const securityMiddleware = [
  ipBlocklistCheck,
  sanitizeRequest,
  detectSuspiciousActivity,
  securityHeaders
];

module.exports = {
  securityMiddleware,
  validate,
  validations,
  sanitizeInput,
  blockIP,
  isIPBlocked,
  logSecurityEvent,
  ipBlocklistCheck,
  sanitizeRequest,
  detectSuspiciousActivity,
  securityHeaders
};
