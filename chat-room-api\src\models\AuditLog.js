const mongoose = require('mongoose');

const auditLogSchema = new mongoose.Schema({
  action: {
    type: String,
    required: true,
    enum: ['create', 'read', 'update', 'delete', 'login', 'logout', 'message_sent', 'message_read']
  },
  resource: {
    type: String,
    required: true,
    enum: ['conversation', 'message', 'user', 'session']
  },
  resourceId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  userType: {
    type: String,
    enum: ['patient', 'nurse', 'admin'],
    required: true
  },
  details: {
    type: Map,
    of: mongoose.Schema.Types.Mixed,
    default: {}
  },
  ipAddress: {
    type: String,
    default: null
  },
  userAgent: {
    type: String,
    default: null
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  sessionId: {
    type: String,
    default: null,
    index: true
  }
}, {
  timestamps: true
});

// Indexes for efficient querying
auditLogSchema.index({ action: 1, timestamp: -1 });
auditLogSchema.index({ resource: 1, resourceId: 1 });
auditLogSchema.index({ userId: 1, timestamp: -1 });
auditLogSchema.index({ timestamp: -1 });

// Static method to create audit log
auditLogSchema.statics.createLog = async function(action, resource, resourceId, userId, userType, details = {}, ipAddress = null, userAgent = null, sessionId = null) {
  const log = new this({
    action,
    resource,
    resourceId,
    userId,
    userType,
    details,
    ipAddress,
    userAgent,
    sessionId
  });
  
  return await log.save();
};

// Static method to get user audit logs
auditLogSchema.statics.getUserLogs = async function(userId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return await this.find({ userId })
    .sort({ timestamp: -1 })
    .skip(skip)
    .limit(limit)
    .lean();
};

// Static method to get resource audit logs
auditLogSchema.statics.getResourceLogs = async function(resource, resourceId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return await this.find({ resource, resourceId })
    .sort({ timestamp: -1 })
    .skip(skip)
    .limit(limit)
    .lean();
};

// Static method to get audit logs by date range
auditLogSchema.statics.getLogsByDateRange = async function(startDate, endDate, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return await this.find({
    timestamp: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  })
  .sort({ timestamp: -1 })
  .skip(skip)
  .limit(limit)
  .lean();
};

// Static method to get audit summary
auditLogSchema.statics.getAuditSummary = async function(startDate, endDate) {
  return await this.aggregate([
    {
      $match: {
        timestamp: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      }
    },
    {
      $group: {
        _id: {
          action: '$action',
          resource: '$resource'
        },
        count: { $sum: 1 }
      }
    },
    {
      $group: {
        _id: '$_id.resource',
        actions: {
          $push: {
            action: '$_id.action',
            count: '$count'
          }
        },
        totalCount: { $sum: '$count' }
      }
    }
  ]);
};

module.exports = mongoose.model('AuditLog', auditLogSchema); 