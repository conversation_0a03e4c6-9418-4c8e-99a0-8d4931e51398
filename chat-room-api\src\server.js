require('dotenv').config();
const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { testConnection: testDBConnection } = require('./config/database');
const { client: redisClient } = require('./config/redis');
const WebSocketManager = require('./utils/websocket');

// Import routes
const chatRoutes = require('./routes/chatRoutes');

const app = express();

app.set('trust proxy', true);
// Middleware
app.use(helmet()); // Security headers
app.use(compression()); // Compress responses
// CORS configuration
const corsOptions = {
  origin: ["http://localhost:3000", "http://localhost:3001", "http://localhost:3002", "http://localhost:3003", "http://localhost:3004", "http://localhost:3005", "http://localhost:8004", "http://localhost:3008", 'https://testcustomerapp.nurserv.com', 'https://testcustomerapi.nurserv.com', 'https://testnurservapp.nurserv.com', 'https://testnurservapi.nurserv.com', 'https://nurseapp.nurserv.com', 'https://customerapp.nurserv.com', 'https://chatapi.nurserv.com'],
  credentials: true,
  methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
  exposedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
  optionsSuccessStatus: 200,
  maxAge: 3600
};

app.use(cors(corsOptions)); // Enable CORS
app.use(express.json()); // Parse JSON bodies
app.use(morgan('dev')); // Logging

// Rate limiting
// const limiter = rateLimit({
//   windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // default 15 min
//   max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
//   standardHeaders: true,
//   legacyHeaders: false
// });
// app.use(limiter);

// Chat routes
app.use('/api/chat', chatRoutes);

// Handle OPTIONS requests for CORS preflight
app.options('*', cors());

app.get("/health", (req, res) => {
  res.json({
    status: "OK",
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development'
  });
});

// Test endpoint
app.get("/test", (req, res) => {
  res.json({message: "Welcome chat room API."})
});



// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Error:', err);
  
  // Handle validation errors
  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: err.errors
    });
  }
  
  // Handle JWT errors
  if (err.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
  
  if (err.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    });
  }
  
  // Default error response
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong!'
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: 'Route not found'
  });
});

// Start server
const PORT = process.env.PORT || 8004;

const startServer = async () => {
  try {
    // Test database connection
    await testDBConnection();
    
    // Ensure Redis client is connected
    if (!redisClient.isOpen) {
      console.log('Connecting to Redis...');
      await redisClient.connect();
    }
    
    // Test Redis connection
    await redisClient.ping();
    console.log('Redis client connected');
    
    const server = app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
      console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`Health check: http://localhost:${PORT}/health`);
      console.log(`WebSocket: ws://localhost:${PORT}`);
    });

    // Initialize WebSocket manager
    const wsManager = new WebSocketManager(server);
    
    // Make WebSocket manager available globally
    global.wsManager = wsManager;

    /** Graceful shutdown **/
    const gracefulShutdown = async (signal) => {
      try {
        console.log(`Received ${signal}. Closing server gracefully...`);
        server.close(() => {
          console.log('HTTP server closed.');
        });

        // Close WebSocket connections
        if (global.wsManager && typeof global.wsManager.closeAll === 'function') {
          await global.wsManager.closeAll();
          console.log('WebSocket connections closed.');
        }

        // Close MongoDB
        await require('./config/database').mongoose.connection.close(false);
        console.log('MongoDB connection closed.');

        // Close Redis
        if (redisClient.isOpen) {
          await redisClient.quit();
          console.log('Redis client disconnected.');
        }

        process.exit(0);
      } catch (shutdownErr) {
        console.error('Error during graceful shutdown:', shutdownErr);
        process.exit(1);
      }
    };

    ['SIGTERM', 'SIGINT'].forEach((sig) => {
      process.on(sig, () => gracefulShutdown(sig));
    });
    
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();