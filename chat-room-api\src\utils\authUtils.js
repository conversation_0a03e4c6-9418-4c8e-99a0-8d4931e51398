/**
 * Shared Authentication Utilities
 * ------------------------------
 * Common JWT verification logic for both REST API and WebSocket connections
 * Supports multi-pool Cognito/JWKS strategy for customer & nurse user pools
 */

const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');
const { promisify } = require('util');
require('dotenv').config();

const { client } = require('../config/redis');

/* -------------------------------------------------------------------------- */
/*                            JWKS CLIENT CONFIGS                             */
/* -------------------------------------------------------------------------- */

const customerUserPoolConfig = {
  jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.CUSTOMER_COGNITO_USER_POOL_ID}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true,
  jwksRequestsPerMinute: 5,
  handleSigningKeyError: (err, cb) => {
    console.error('Customer JWKS Error:', err);
    if (
      process.env.NODE_ENV === 'development' &&
      process.env.USE_TEST_AUTH === 'true'
    ) {
      return cb(null, null);
    }
    return cb(err);
  }
};

const nurseUserPoolConfig = {
  jwksUri: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.NURSE_COGNITO_USER_POOL_ID}/.well-known/jwks.json`,
  cache: true,
  rateLimit: true,
  jwksRequestsPerMinute: 5,
  handleSigningKeyError: (err, cb) => {
    console.error('Nurse JWKS Error:', err);
    if (
      process.env.NODE_ENV === 'development' &&
      process.env.USE_TEST_AUTH === 'true'
    ) {
      return cb(null, null);
    }
    return cb(err);
  }
};

const customerJwksClient = jwksClient(customerUserPoolConfig);
const nurseJwksClient = jwksClient(nurseUserPoolConfig);

const getCustomerSigningKey = promisify(customerJwksClient.getSigningKey);
const getNurseSigningKey   = promisify(nurseJwksClient.getSigningKey);

const USER_POOLS = {
  customer: {
    poolId: process.env.CUSTOMER_COGNITO_USER_POOL_ID,
    clientId: process.env.CUSTOMER_COGNITO_APP_CLIENT_ID,
    issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.CUSTOMER_COGNITO_USER_POOL_ID}`,
    getSigningKey: getCustomerSigningKey,
    userType: 'patient'
  },
  nurse: {
    poolId: process.env.NURSE_COGNITO_USER_POOL_ID,
    clientId: process.env.NURSE_COGNITO_APP_CLIENT_ID,
    issuer: `https://cognito-idp.${process.env.AWS_REGION}.amazonaws.com/${process.env.NURSE_COGNITO_USER_POOL_ID}`,
    getSigningKey: getNurseSigningKey,
    userType: 'nurse'
  }
};

/**
 * Verify a token against a given pool (helper).
 */
const tryVerifyWithPool = async (token, poolConfig, kid) => {
  // Get signing key
  const key = await poolConfig.getSigningKey(kid);
  if (!key) throw new Error(`No signing key for kid ${kid}`);

  const signingKey = key.getPublicKey ? key.getPublicKey() : key.rsaPublicKey;
  if (!signingKey) throw new Error('Failed to acquire public key');

  const verifiedToken = jwt.verify(token, signingKey, {
    algorithms: ['RS256'],
    issuer: poolConfig.issuer,
    audience: poolConfig.clientId
  });

  return { verifiedToken, userType: poolConfig.userType };
};

/**
 * Comprehensive JWT token verification
 * @param {string} token - JWT token to verify
 * @returns {Object} User information including verifiedToken, userType, userId, email, username
 */
async function verifyAndGetUser(token) {
  // Development shortcut
  if (
    process.env.NODE_ENV === 'development' &&
    process.env.USE_TEST_AUTH === 'true'
  ) {
    try {
      const decodedDev = jwt.verify(token, process.env.JWT_TEST_SECRET || 'test-secret');
      return {
        verifiedToken: decodedDev,
        userType: decodedDev.userType || 'patient',
        userId: decodedDev.sub,
        email: decodedDev.email,
        username: decodedDev.preferred_username
      };
    } catch (err) {
      console.error('Development token verification error:', err);
      throw new Error('Invalid development token');
    }
  }

  // Cognito verification
  let verificationResult = null;
  let lastError = null;

  // Decode without verifying to get kid & issuer
  const decodedHeaderPayload = jwt.decode(token, { complete: true });
  if (!decodedHeaderPayload) {
    throw new Error('Invalid token format');
  }

  const { kid } = decodedHeaderPayload.header;
  const tokenIssuer = decodedHeaderPayload.payload.iss;

  // Prioritise pool with matching issuer
  let poolsToTry = Object.values(USER_POOLS);
  const matchingPool = poolsToTry.find(p => p.issuer === tokenIssuer);
  if (matchingPool) {
    poolsToTry = [matchingPool, ...poolsToTry.filter(p => p !== matchingPool)];
  }

  for (const pool of poolsToTry) {
    try {
      verificationResult = await tryVerifyWithPool(token, pool, kid);
      break; // success
    } catch (err) {
      lastError = err;
    }
  }

  if (!verificationResult) {
    console.error('Token verification failed with all user pools', lastError);
    throw new Error(lastError ? lastError.message : 'Token verification failed');
  }

  const { verifiedToken, userType } = verificationResult;
  const userId = verifiedToken.sub;

  return {
    verifiedToken,
    userType,
    userId,
    email: verifiedToken.email,
    username: verifiedToken['cognito:username'] || verifiedToken.preferred_username,
    tokenUse: verifiedToken.token_use,
    clientId: verifiedToken.client_id || verifiedToken.aud
  };
}

/**
 * Validate and refresh user session
 * @param {string} userId - User ID
 * @param {string} userType - User type (patient/nurse)
 * @returns {boolean} Whether session is valid
 */
async function validateAndRefreshSession(userId, userType) {
  try {
    // Check if Redis is available
    if (!client || !client.isOpen) {
      console.warn('[auth-utils] Redis not available, skipping session validation');
      return process.env.NODE_ENV === 'development';
    }

    const sessionKey = `session:${userId}:${userType}`;
    const session = await client.get(sessionKey);
console.log("session", session);
    if (!session) {
      // Auto-create session in development mode
      //if (process.env.NODE_ENV === 'development') {
        await client.set(sessionKey, JSON.stringify({ createdAt: Date.now() }), { EX: 1800 });
        return true;
      //}
      return false;
    }

    // Extend session TTL
    await client.expire(sessionKey, 1800);
    return true;
  } catch (error) {
    console.error('[auth-utils] Session validation error:', error);
    return process.env.NODE_ENV === 'development';
  }
}

/**
 * Extract token from request (supports both query string and Authorization header)
 * @param {Object} req - Request object
 * @returns {string|null} Token or null if not found
 */
function extractToken(req) {
  // Try to get token from query string
  const url = new URL(req.url, `http://${req.headers.host}`);
  let token = url.searchParams.get('token');

  // If not in query string, try Authorization header
  if (!token && req.headers.authorization) {
    token = req.headers.authorization.split(' ')[1];
  }

  return token;
}

module.exports = {
  verifyAndGetUser,
  validateAndRefreshSession,
  extractToken,
  USER_POOLS
}; 