const { client, CACHE_TTL } = require('../config/redis');

class CacheManager {
  // Set cache with TTL
  static async set(key, value, ttl = CACHE_TTL.CHAT_HISTORY) {
    try {
      await client.setEx(key, ttl, JSON.stringify(value));
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }

  // Get cache value
  static async get(key) {
    try {
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }

  // Delete cache key
  static async del(key) {
    try {
      await client.del(key);
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }

  // Delete multiple cache keys with pattern
  static async delPattern(pattern) {
    try {
      const keys = await client.keys(pattern);
      if (keys.length > 0) {
        await client.del(keys);
      }
      return true;
    } catch (error) {
      console.error('Cache delete pattern error:', error);
      return false;
    }
  }

  // Invalidate conversation cache
  static async invalidateConversationCache(conversationId) {
    try {
      const patterns = [
        `conversation:${conversationId}`,
        `messages:${conversationId}:*`,
        `conversations:patient:*`,
        `conversations:nurse:*`
      ];

      for (const pattern of patterns) {
        await this.delPattern(pattern);
      }

      return true;
    } catch (error) {
      console.error('Invalidate conversation cache error:', error);
      return false;
    }
  }

  // Invalidate user cache
  static async invalidateUserCache(userId, userType) {
    try {
      const patterns = [
        `conversations:${userType}:${userId}:*`,
        `active_count:${userType}:${userId}`,
        `session:${userId}:${userType}`
      ];

      for (const pattern of patterns) {
        await this.delPattern(pattern);
      }

      return true;
    } catch (error) {
      console.error('Invalidate user cache error:', error);
      return false;
    }
  }

  // Cache conversation messages with pagination
  static async cacheMessages(conversationId, page, limit, messages) {
    try {
      const key = `messages:${conversationId}:${page}:${limit}`;
      await this.set(key, messages, CACHE_TTL.CHAT_HISTORY);
      return true;
    } catch (error) {
      console.error('Cache messages error:', error);
      return false;
    }
  }

  // Cache user conversations
  static async cacheUserConversations(userId, userType, page, limit, conversations) {
    try {
      const key = `conversations:${userType}:${userId}:${page}:${limit}`;
      await this.set(key, conversations, CACHE_TTL.CHAT_HISTORY);
      return true;
    } catch (error) {
      console.error('Cache user conversations error:', error);
      return false;
    }
  }

  // Cache conversation details
  static async cacheConversation(conversationId, conversation) {
    try {
      const key = `conversation:${conversationId}`;
      const ttl = conversation.status === 'active' ? CACHE_TTL.ACTIVE_CHAT : CACHE_TTL.INACTIVE_CHAT;
      await this.set(key, conversation, ttl);
      return true;
    } catch (error) {
      console.error('Cache conversation error:', error);
      return false;
    }
  }

  // Cache active conversations count
  static async cacheActiveCount(userId, userType, count) {
    try {
      const key = `active_count:${userType}:${userId}`;
      await this.set(key, count, 300); // 5 minutes TTL
      return true;
    } catch (error) {
      console.error('Cache active count error:', error);
      return false;
    }
  }

  // Get cache statistics
  static async getCacheStats() {
    try {
      const info = await client.info('memory');
      const keys = await client.dbSize();
      
      return {
        keys,
        info: info.split('\r\n').reduce((acc, line) => {
          const [key, value] = line.split(':');
          if (key && value) {
            acc[key] = value;
          }
          return acc;
        }, {})
      };
    } catch (error) {
      console.error('Get cache stats error:', error);
      return null;
    }
  }

  // Clean up expired cache entries
  static async cleanup() {
    try {
      // This is handled automatically by Redis with TTL
      // But we can add custom cleanup logic here if needed
      console.log('Cache cleanup completed');
      return true;
    } catch (error) {
      console.error('Cache cleanup error:', error);
      return false;
    }
  }

  // Warm up cache for frequently accessed data
  static async warmupCache() {
    try {
      // This can be used to pre-load frequently accessed conversations
      // Implementation depends on your specific use case
      console.log('Cache warmup completed');
      return true;
    } catch (error) {
      console.error('Cache warmup error:', error);
      return false;
    }
  }
}

module.exports = CacheManager; 