#!/usr/bin/env node
/**
 * WebSocket Connection Test Script
 * 
 * Tests WebSocket connectivity to the chat-room-api server with proper authentication.
 * 
 * Usage:
 *   node test-websocket-connection.js [options]
 * 
 * Options:
 *   --wsUrl=<url>       WebSocket URL (default: ws://localhost:8004/ws)
 *   --token=<token>     Authentication token (required)
 *   --userId=<id>       User ID (required)
 *   --userType=<type>   User type: 'patient' or 'nurse' (required)
 *   --conversationId=<id> Conversation ID to join (optional)
 *   --verbose           Enable verbose logging
 * 
 * Example:
 *   node test-websocket-connection.js --token="eyJhbG..." --userId="123" --userType="patient"
 */

const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

// Parse command line arguments
const args = process.argv.slice(2).reduce((acc, arg) => {
  if (arg.startsWith('--')) {
    const [key, value] = arg.substring(2).split('=');
    acc[key] = value === undefined ? true : value;
  }
  return acc;
}, {});

// Configuration with defaults
const config = {
  wsUrl: args.wsUrl || 'ws://localhost:8004/ws',
  token: args.token,
  userId: args.userId,
  userType: args.userType,
  conversationId: args.conversationId || uuidv4(),
  verbose: args.verbose || false
};

// Validate required parameters
if (!config.token) {
  console.error('Error: --token parameter is required');
  process.exit(1);
}

if (!config.userId) {
  console.error('Error: --userId parameter is required');
  process.exit(1);
}

if (!config.userType || !['patient', 'nurse'].includes(config.userType)) {
  console.error('Error: --userType must be either "patient" or "nurse"');
  process.exit(1);
}

// Logging utility
const log = {
  info: (...args) => console.log('\x1b[36m[INFO]\x1b[0m', ...args),
  success: (...args) => console.log('\x1b[32m[SUCCESS]\x1b[0m', ...args),
  error: (...args) => console.error('\x1b[31m[ERROR]\x1b[0m', ...args),
  warn: (...args) => console.warn('\x1b[33m[WARN]\x1b[0m', ...args),
  debug: (...args) => {
    if (config.verbose) {
      console.log('\x1b[90m[DEBUG]\x1b[0m', ...args);
    }
  }
};

// Format the full WebSocket URL with authentication parameters
const getFullWsUrl = () => {
  const url = new URL(config.wsUrl);
  url.searchParams.append('token', config.token);
  url.searchParams.append('userId', config.userId);
  url.searchParams.append('userType', config.userType);
  return url.toString();
};

// Test stages
const stages = {
  connection: {
    name: 'Connection',
    passed: false,
    description: 'Establish WebSocket connection with authentication'
  },
  authentication: {
    name: 'Authentication',
    passed: false,
    description: 'Verify token is accepted and session is created'
  },
  joinConversation: {
    name: 'Join Conversation',
    passed: false,
    description: 'Join a conversation room'
  },
  sendMessage: {
    name: 'Send Message',
    passed: false,
    description: 'Send a text message to the conversation'
  },
  receiveMessage: {
    name: 'Receive Message',
    passed: false,
    description: 'Receive message confirmation from server'
  }
};

// WebSocket event handlers
let ws;
let connectionTimeout;
let testTimeout;

// Main test function
async function runTest() {
  log.info(`Starting WebSocket connection test to ${config.wsUrl}`);
  log.info(`User: ${config.userId} (${config.userType})`);
  log.info(`Conversation: ${config.conversationId}`);
  
  // Set a global timeout for the entire test
  testTimeout = setTimeout(() => {
    log.error('Test timed out after 30 seconds');
    printResults();
    process.exit(1);
  }, 30000);
  
  try {
    // Connect to WebSocket server
    const fullWsUrl = getFullWsUrl();
    log.debug(`Connecting to: ${fullWsUrl}`);
    
    ws = new WebSocket(fullWsUrl);
    
    // Set connection timeout
    connectionTimeout = setTimeout(() => {
      log.error('Connection timed out after 10 seconds');
      ws.terminate();
      process.exit(1);
    }, 10000);
    
    // Connection opened
    ws.on('open', () => {
      clearTimeout(connectionTimeout);
      stages.connection.passed = true;
      log.success('WebSocket connection established');
      
      // Wait for authentication success message from server
      // This will be handled in the message event
    });
    
    // Listen for messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        log.debug('Received message:', message);
        
        // Handle different message types
        switch (message.type) {
          case 'CONNECTION_SUCCESS':
          case 'connection': // Legacy support
            stages.authentication.passed = true;
            log.success('Authentication successful');
            
            // Join a conversation
            sendJoinConversation();
            break;
            
          case 'user_joined':
            if (message.conversationId === config.conversationId) {
              stages.joinConversation.passed = true;
              log.success(`Joined conversation: ${config.conversationId}`);
              
              // Send a test message
              sendTestMessage();
            }
            break;
            
          case 'TEXT_MESSAGE':
          case 'new_message': // Legacy support
            if (message.conversationId === config.conversationId) {
              stages.receiveMessage.passed = true;
              log.success('Received message confirmation');
              
              // Test completed successfully
              setTimeout(() => {
                ws.close();
                clearTimeout(testTimeout);
                printResults();
                process.exit(0);
              }, 1000);
            }
            break;
            
          default:
            log.debug(`Unhandled message type: ${message.type}`);
        }
      } catch (err) {
        log.error('Error parsing message:', err);
      }
    });
    
    // Handle errors
    ws.on('error', (error) => {
      log.error('WebSocket error:', error.message);
    });
    
    // Connection closed
    ws.on('close', (code, reason) => {
      log.info(`WebSocket connection closed: ${code} ${reason}`);
      if (!stages.connection.passed) {
        log.error('Connection failed');
      }
    });
    
  } catch (error) {
    log.error('Test failed:', error.message);
    process.exit(1);
  }
}

// Send join conversation message
function sendJoinConversation() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    log.error('WebSocket not connected');
    return;
  }
  
  const joinMessage = {
    type: 'JOIN_CONVERSATION',
    conversationId: config.conversationId
  };
  
  log.debug('Sending join conversation:', joinMessage);
  ws.send(JSON.stringify(joinMessage));
}

// Send test message
function sendTestMessage() {
  if (!ws || ws.readyState !== WebSocket.OPEN) {
    log.error('WebSocket not connected');
    return;
  }
  
  const testMessage = {
    type: 'TEXT_MESSAGE',
    conversationId: config.conversationId,
    content: `Test message from WebSocket test script at ${new Date().toISOString()}`
  };
  
  log.debug('Sending test message:', testMessage);
  ws.send(JSON.stringify(testMessage));
  stages.sendMessage.passed = true;
  log.success('Test message sent');
}

// Print test results
function printResults() {
  console.log('\n----- TEST RESULTS -----');
  
  let allPassed = true;
  Object.values(stages).forEach(stage => {
    const status = stage.passed ? '\x1b[32m✓ PASS\x1b[0m' : '\x1b[31m✗ FAIL\x1b[0m';
    console.log(`${status} ${stage.name}: ${stage.description}`);
    if (!stage.passed) allPassed = false;
  });
  
  console.log('\n-----------------------');
  if (allPassed) {
    console.log('\x1b[32mALL TESTS PASSED\x1b[0m');
  } else {
    console.log('\x1b[31mSOME TESTS FAILED\x1b[0m');
  }
  console.log('-----------------------\n');
}

// Run the test
runTest();

// Handle process termination
process.on('SIGINT', () => {
  log.info('Test interrupted');
  if (ws) ws.close();
  process.exit(0);
});
