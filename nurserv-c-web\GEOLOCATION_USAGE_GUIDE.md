# Geolocation Usage Guide - LocationCard Component

## Overview

This document explains how the LocationCard component handles geolocation in a privacy-conscious and Sonar-compliant manner.

## Sonar Compliance Improvements

### 1. **Explicit User Consent**

- Geolocation is disabled by default (`enableGeolocation = false`)
- User consent is required before accessing location (`requireUserConsent = true`)
- Clear consent dialog explains the purpose of location access
- Users can decline and use alternative methods

### 2. **Clear Purpose Declaration**

- `geolocationPurpose` prop allows specifying why location is needed
- Default purpose: "to provide location-based services"
- Purpose is displayed in the consent dialog

### 3. **Alternative Methods Always Available**

- Search functionality for manual location entry
- Click/drag on map for manual selection
- Network-based location as fallback (IP-based, less accurate)
- No functionality is blocked if geolocation is denied

### 4. **Privacy Protection**

- Location data is not stored or transmitted unnecessarily
- Only used for immediate purpose (address selection)
- Clear error messages guide users to alternatives
- Timeout and error handling prevent hanging requests

## Usage Examples

### Basic Usage (No Geolocation)

```tsx
<LocationCard
  onLocationSelect={location => console.log(location)}
  title='Select Delivery Location'
  enableGeolocation={false} // Explicitly disabled
/>
```

### With User-Consented Geolocation

```tsx
<LocationCard
  onLocationSelect={location => console.log(location)}
  title='Select Your Location'
  enableGeolocation={true}
  geolocationPurpose='to find nearby services'
  requireUserConsent={true}
  showSearchHint={true}
/>
```

### For Emergency Services (Justified High-Priority Use)

```tsx
<LocationCard
  onLocationSelect={location => handleEmergencyLocation(location)}
  title='Emergency Location'
  enableGeolocation={true}
  geolocationPurpose='for emergency response services'
  requireUserConsent={false} // Only for critical emergency use
  buttonText='Confirm Emergency Location'
/>
```

## Security Features

1. **Timeout Protection**: 10-second timeout prevents hanging requests
2. **Error Handling**: Comprehensive error messages for all failure scenarios
3. **Fallback Strategy**: Multiple alternatives if geolocation fails
4. **No Persistent Storage**: Location data is not cached or stored
5. **User Control**: Users can always decline and use manual methods

## Best Practices

1. **Only enable geolocation when truly necessary**
2. **Always provide clear purpose explanation**
3. **Ensure alternative methods work without geolocation**
4. **Test with geolocation disabled**
5. **Handle all error scenarios gracefully**

## Sonar Rule Compliance

This implementation addresses the Sonar rule "Make sure the use of the geolocation is necessary" by:

- ✅ Requiring explicit enablement (`enableGeolocation` prop)
- ✅ Providing clear purpose documentation
- ✅ Implementing user consent mechanism
- ✅ Offering multiple alternative methods
- ✅ Using minimal necessary permissions
- ✅ Not storing location data unnecessarily
- ✅ Providing comprehensive error handling
- ✅ Documenting the justification for geolocation use
