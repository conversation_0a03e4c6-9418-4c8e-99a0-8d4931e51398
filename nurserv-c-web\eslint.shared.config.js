import { FlatCompat } from "@eslint/eslintrc";
import js from "@eslint/js";
import path from "path";
import { fileURLToPath } from "url";
import tseslint from "typescript-eslint";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
});


const baseIgnores = [
  "**/node_modules/**",
  "dist/**",
  "build/**",
  ".git/**",
  "tailwind.config.ts",
  "vite.config.ts",
  "src/types/google-maps.d.ts",
];


const reactSettings = {
  react: {
    version: "detect",
  },
};


const unusedImportsRules = {
  "unused-imports/no-unused-imports": "error",
  "unused-imports/no-unused-vars": [
    "warn",
    {
      vars: "all",
      varsIgnorePattern: "^_",
      args: "after-used",
      argsIgnorePattern: "^_",
    },
  ],
};


const commonReactRules = {
  "react/react-in-jsx-scope": "off",
  "react/prop-types": "off",
  "react/no-unknown-property": "off",
};


const commonTypeScriptRules = {
  "no-unused-vars": "off",
  "@typescript-eslint/no-unused-vars": "off",
  "@typescript-eslint/explicit-function-return-type": "off",
  "@typescript-eslint/explicit-module-boundary-types": "off",
  "@typescript-eslint/no-explicit-any": "warn",
};


const typeScriptLanguageOptions = {
  parser: tseslint.parser,
  parserOptions: {
    project: "./tsconfig.app.json",
    ecmaVersion: "latest",
    sourceType: "module",
    ecmaFeatures: {
      jsx: true,
    },
  },
};


export function createEslintConfig(options = {}) {
  const {
    allowComments = true,
    includeSpacedCommentRule = false,
    additionalIgnores = [],
    additionalPlugins = [],
    additionalRules = {},
  } = options;

  
  const plugins = ["react", "react-hooks", "unused-imports", ...additionalPlugins];
  if (!allowComments) {
    plugins.push("no-comments");
  }

  
  const ignores = [...baseIgnores, ...additionalIgnores];

  
  const typeScriptRules = {
    ...tseslint.configs.recommendedTypeChecked.rules,
    ...unusedImportsRules,
    ...commonReactRules,
    ...commonTypeScriptRules,
    ...additionalRules,
  };

  
  if (includeSpacedCommentRule) {
    typeScriptRules["spaced-comment"] = [
      "error",
      "always",
      {
        line: {
          markers: ["/"],
          exceptions: ["-", "+"],
        },
        block: {
          markers: ["*"],
          exceptions: ["*"],
          balanced: true,
        },
      },
    ];
  }

  
  if (!allowComments) {
    typeScriptRules["no-comments/disallowComments"] = "error";
  }

  
  const javaScriptRules = {
    ...unusedImportsRules,
    ...commonReactRules,
    ...additionalRules,
  };

  if (!allowComments) {
    javaScriptRules["no-comments/disallowComments"] = "error";
  }

  return [
    
    {
      ignores,
    },

    
    js.configs.recommended,
    ...tseslint.configs.recommended,

    
    ...compat.config({
      extends: [
        "plugin:react/recommended",
        "plugin:react-hooks/recommended",
        "plugin:prettier/recommended",
      ],
      plugins,
    }),

    
    {
      files: ["*.config.js", ".eslintrc.js"],
      rules: {
        "@typescript-eslint/await-thenable": "off",
        "@typescript-eslint/no-floating-promises": "off",
        "@typescript-eslint/no-misused-promises": "off",
        ...Object.fromEntries(
          Object.keys(tseslint.configs.recommendedTypeChecked.rules || {}).map(
            (key) => [key, "off"]
          )
        ),
      },
    },

    
    {
      files: ["**/*.ts", "**/*.tsx"],
      languageOptions: typeScriptLanguageOptions,
      rules: typeScriptRules,
      settings: reactSettings,
    },

    
    {
      files: ["**/*.js", "**/*.jsx"],
      rules: javaScriptRules,
      settings: reactSettings,
    },
  ];
}
