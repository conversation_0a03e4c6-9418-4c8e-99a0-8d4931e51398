import React from 'react';
import { ChevronDown, MapPin, MapPinHouse, LucideIcon } from 'lucide-react';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { SerializedError } from '@reduxjs/toolkit';
import { Address, ApiResponse } from '@/store/api/apiSlice';

interface AddressDropdownProps {
  selectedAddress: Address | null;
  displayAddress: string;
  displayAddressName: string;
  addressIcon: LucideIcon;
  hasAddresses: boolean;
  addressesResponse: ApiResponse<Address[]> | null;
  showAddressDropdown: boolean;
  onToggleDropdown: () => void;
  onCloseDropdown: () => void;
  onAddressSelect: (address: Address) => void;
  onDropdownKeyDown: (e: React.KeyboardEvent) => void;
  onOpenRef: React.RefObject<HTMLDivElement>;
  addressButtonRefs: React.MutableRefObject<(HTMLButtonElement | null)[]>;
  addressesError: FetchBaseQueryError | SerializedError | null;
  onAddressRetry: () => void;
  addressesLoading: boolean;
}

const AddressDropdown: React.FC<AddressDropdownProps> = ({
  selectedAddress,
  displayAddress,
  displayAddressName,
  addressIcon: IconComponent,
  hasAddresses,
  addressesResponse,
  showAddressDropdown,
  onToggleDropdown,
  onCloseDropdown,
  onAddressSelect,
  onDropdownKeyDown,
  onOpenRef,
  addressButtonRefs,
  addressesError,
  onAddressRetry,
  addressesLoading,
}) => {
  const renderAddressOptions = () => {
    if (!hasAddresses || !addressesResponse?.data) return null;

    const addresses = addressesResponse.data;
    let orderedAddresses = addresses;

    if (selectedAddress) {
      orderedAddresses = [
        selectedAddress,
        ...addresses.filter((addr: Address) => addr.id !== selectedAddress.id),
      ];
    }

    return orderedAddresses.map((addr: Address, index: number) => (
      <button
        key={addr.id}
        ref={el => {
          addressButtonRefs.current[index] = el;
        }}
        className={`w-full text-left p-2 rounded cursor-pointer bg-transparent border-none focus:outline-none focus:ring-2 focus:ring-[#f09e22] focus:ring-opacity-50 ${
          selectedAddress?.id === addr.id
            ? 'border-l-4 border-r-4 border-[#f09e22] bg-white bg-opacity-70 rounded-lg '
            : 'hover:bg-white hover:bg-opacity-50'
        }`}
        onClick={() => onAddressSelect(addr)}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onAddressSelect(addr);
          } else if (e.key === 'Escape') {
            onCloseDropdown();
          } else if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            onDropdownKeyDown(e);
          }
        }}
        role='menuitem'
        aria-label={`Select ${addr.name} address: ${addr.address}`}
        tabIndex={showAddressDropdown ? 0 : -1}
      >
        <div className='flex items-center gap-2 mb-1'>
          {addr.icon === 'home' ? (
            <MapPinHouse className='w-4 h-4 text-nursery-darkBlue ' />
          ) : (
            <MapPin className='w-4 h-4 text-nursery-darkBlue' />
          )}
          <span className='font-medium text-sm text-nursery-darkBlue'>
            {addr.name}
          </span>
        </div>
        <p className='text-xs text-gray-800 ml-6'>{addr.address}</p>
      </button>
    ));
  };

  return (
    <div className='relative'>
      <button
        className='md:text-lg text-sm font-bold text-white flex flex-row gap-1 items-center justify-start bg-transparent border-none p-0 cursor-pointer'
        onClick={onCloseDropdown}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onCloseDropdown();
          }
        }}
        aria-label='Close address dropdown'
      >
        <span className='text-white'>
          <IconComponent className='w-4 h-4 md:w-5 md:h-5' />
        </span>
        {displayAddressName.toUpperCase()}
      </button>

      <button
        className={`flex flex-row items-end justify-start bg-transparent border-none p-0 ${
          hasAddresses && addressesResponse?.data?.length > 1
            ? 'cursor-pointer hover:text-white focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 rounded'
            : 'cursor-default'
        }`}
        onClick={() => {
          if (hasAddresses && addressesResponse?.data?.length > 1) {
            onToggleDropdown();
          }
        }}
        onKeyDown={e => {
          if (
            (e.key === 'Enter' || e.key === ' ') &&
            hasAddresses &&
            addressesResponse?.data?.length > 1
          ) {
            e.preventDefault();
            onToggleDropdown();
          }
        }}
        disabled={
          !hasAddresses ||
          !addressesResponse?.data ||
          addressesResponse.data.length <= 1
        }
        aria-label={`Select address. Current: ${displayAddress}`}
        aria-expanded={showAddressDropdown}
        aria-haspopup='menu'
      >
        <p className='md:text-sm text-sm text-white font-medium truncate max-w-[200px] md:max-w-[250px]'>
          {displayAddress !== 'Address not available'
            ? displayAddress.split(',').slice(0, 3).join(',') + '...'
            : displayAddress}
        </p>
        {hasAddresses && addressesResponse?.data?.length > 1 && (
          <span>
            <ChevronDown
              size={16}
              strokeWidth={'2.5px'}
              className={`transition-transform text-white ${
                showAddressDropdown ? 'rotate-180' : ''
              }`}
            />
          </span>
        )}
      </button>

      {showAddressDropdown && hasAddresses && (
        <div
          ref={onOpenRef}
          className='absolute mt-1 p-0 bg-white bg-opacity-70 backdrop-blur-md shadow-lg rounded-lg z-10 max-w-xs min-w-[250px] right-0 left-[-23px] sm:right-auto sm:left-0'
          role='menu'
          aria-label='Address selection'
        >
          <div className='max-h-60 overflow-y-auto'>
            {renderAddressOptions()}
          </div>
        </div>
      )}

      {}
      {addressesError && (
        <div className='absolute mt-1 p-2 bg-red-50 border border-red-200 rounded-md z-10 max-w-xs'>
          <p className='text-xs text-red-600 mb-2'>
            Failed to load addresses. Please try again.
          </p>
          <button
            onClick={onAddressRetry}
            className='text-xs bg-red-100 hover:bg-red-200 px-2 py-1 rounded'
          >
            Retry
          </button>
        </div>
      )}

      {}
      {!addressesLoading && !addressesError && !hasAddresses && (
        <div className='absolute mt-1 p-2 bg-yellow-50 border border-yellow-200 rounded-md z-10 max-w-xs'>
          <p className='text-xs text-yellow-600'>
            No addresses found. Using fallback address.
          </p>
          <p className='text-xs text-yellow-500 mt-1'>
            Debug: Response = {JSON.stringify(addressesResponse)}
          </p>
        </div>
      )}
    </div>
  );
};

export default AddressDropdown;
