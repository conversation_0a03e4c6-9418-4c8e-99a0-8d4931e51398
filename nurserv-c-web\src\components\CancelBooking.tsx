import React, { useState } from 'react';
import { ArrowLeft, User, AlertCircle } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { showErrorToast, handleApiError } from '@/utils/toast';
import bg from '../../public/Images/bg4.png';
import {
  useUpdateBookingStatusMutation,
  useGetCancellationReasonsQuery,
} from '../store/api/apiSlice';

const CancelBooking = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const booking = location.state?.booking;
  const [updateBookingStatus] = useUpdateBookingStatusMutation();
  const [selectedReason, setSelectedReason] = useState('');
  const [selectedReasonId, setSelectedReasonId] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    data: cancellationReasons = [],
    isLoading: reasonsLoading,
    error: reasonsError,
  } = useGetCancellationReasonsQuery();

  const reasonsList = cancellationReasons.filter(
    reason => reason.isActive === 1
  );

  const formatDate = dateString => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'short',
        day: 'numeric',
        year: 'numeric',
      });
    } catch {
      return dateString;
    }
  };

  const formatTime12Hour = time24 => {
    if (!time24) return '';
    const [hourStr, minuteStr] = time24.split(':');
    let hour = parseInt(hourStr, 10);
    const minute = minuteStr || '00';
    const ampm = hour >= 12 ? 'PM' : 'AM';
    hour = hour % 12 || 12;
    return `${hour}:${minute} ${ampm}`;
  };

  const renderCancellationReasonsContent = () => {
    if (reasonsLoading) {
      return (
        <div className='flex justify-center p-4'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-darkBlue'></div>
        </div>
      );
    }

    if (reasonsError) {
      return (
        <div className='p-4 bg-red-50 rounded-lg border border-red-200'>
          <div className='flex items-center gap-2 mb-2'>
            <AlertCircle className='h-5 w-5 text-red-500' />
            <p className='text-red-600 font-medium'>
              Error loading cancellation reasons
            </p>
          </div>
          <p className='text-sm text-red-500'>
            Unable to load cancellation reasons. Please try again later.
          </p>
          <button
            onClick={() => window.location.reload()}
            className='mt-3 px-4 py-2 bg-red-100 text-red-700 rounded-md text-sm hover:bg-red-200 transition-colors'
          >
            Retry
          </button>
        </div>
      );
    }

    if (cancellationReasons.length === 0) {
      return (
        <div className='p-4 bg-yellow-50 rounded-lg border border-yellow-200'>
          <div className='flex items-center gap-2'>
            <AlertCircle className='h-5 w-5 text-yellow-500' />
            <p className='text-yellow-600 font-medium'>
              No cancellation reasons available
            </p>
          </div>
          <p className='text-sm text-yellow-500 mt-1'>
            Please contact support for assistance.
          </p>
        </div>
      );
    }

    return (
      <div className='space-y-3 p-4'>
        {reasonsList.map(reason => (
          <label
            key={reason.id}
            className='flex items-start gap-3 p-3 rounded-lg border border-gray-200 cursor-pointer hover:bg-gray-50 transition-colors'
          >
            <input
              type='radio'
              name='cancellationReason'
              value={reason.cancellation_reasons}
              checked={selectedReason === reason.cancellation_reasons}
              onChange={() => {
                setSelectedReason(reason.cancellation_reasons);
                setSelectedReasonId(reason.id);
              }}
              className='mt-1 w-4 h-4 hover:bg-[F2F2F2] text-nursery-blue border-gray-300 focus:ring-nursery-blue'
            />
            <span className='text-sm text-gray-700 leading-relaxed'>
              {reason.cancellation_reasons}
            </span>
          </label>
        ))}
      </div>
    );
  };

  const handleConfirmCancellation = async () => {
    if (!selectedReason) {
      alert('Please select a reason for cancellation');
      return;
    }

    setIsSubmitting(true);
    try {
      await updateBookingStatus({
        booking_id: booking.booking_id,
        booking_status: 'Cancelled',
        cancellation_reason_id: selectedReasonId,
      }).unwrap();

      navigate('/cancellation-success', {
        state: {
          booking: booking,
          cancellationReason: selectedReason,
          cancellationReasonId: selectedReasonId,
          statusAlreadyUpdated: true,
        },
      });
    } catch (error: unknown) {
      console.error('Failed to cancel booking:', error);

      const apiError = error as {
        data?: { error?: string };
        message?: string;
        status?: number;
      };
      const errorMessage = apiError?.data?.error || apiError?.message;
      const statusCode = apiError?.status;

      switch (statusCode) {
        case 400:
          if (errorMessage?.includes('Cannot cancel a completed booking')) {
            showErrorToast(
              'This booking has already been completed and cannot be cancelled.'
            );
          } else if (
            errorMessage?.includes('Cannot cancel a cancelled booking')
          ) {
            showErrorToast('This booking has already been cancelled.');
          } else if (errorMessage?.includes('Invalid cancellation reason ID')) {
            showErrorToast('Invalid cancellation reason. Please try again.');
          } else {
            showErrorToast(
              errorMessage || 'Invalid request. Please try again.'
            );
          }
          break;
        case 403:
          if (errorMessage?.includes('You can only modify your own bookings')) {
            showErrorToast('You can only cancel your own bookings.');
          } else if (
            errorMessage?.includes('Customers can only cancel bookings')
          ) {
            showErrorToast(
              'You can only cancel bookings, not modify their status.'
            );
          } else {
            showErrorToast('Access denied. Please check your permissions.');
          }
          break;
        case 404:
          showErrorToast('Booking not found. It may have been deleted.');
          break;
        default:
          handleApiError(error, {
            [statusCode]:
              errorMessage || 'Failed to cancel booking. Please try again.',
          });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!booking) {
    return (
      <div className='min-h-screen bg-white flex items-center justify-center'>
        <div className='text-center'>
          <p className='text-gray-600 mb-4'>No booking data found</p>
          <button
            onClick={() => navigate('/schedule')}
            className='bg-nursery-blue text-white px-4 py-2 rounded-lg'
          >
            Go to Schedule
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-white flex flex-col'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Cancel Booking</h1>
        </div>
      </header>

      <div className='flex-1 p-6'>
        {}
        <div className='bg-[#F2F2F2] rounded-lg p-4 mb-6 items-center'>
          <div className='flex justify-between items-start mb-4 text-sm gap-6'>
            <div>
              <div className=' text-gray-600'>Booking ID</div>
              <div className='font-semibold text-gray-900'>
                #{booking.booking_id}
              </div>
            </div>
            <div>
              <div className=' text-gray-600'>Service Date</div>
              <div className='font-semibold text-gray-900'>
                {formatDate(booking.booked_date)}
              </div>
            </div>
            <div>
              <div className='text-gray-600'>Service Time</div>
              <div className='font-semibold text-gray-900'>
                {formatTime12Hour(booking.booked_slot)}
              </div>
            </div>
          </div>

          <div className='flex items-center gap-3 mb-3'>
            <div className='w-10 h-10 bg-nursery-blue rounded-full flex items-center justify-center'>
              <User className='w-6 h-6 text-white' />
            </div>
            <div>
              <div className='text-sm text-gray-600'>Nurse</div>
              <div className='font-semibold text-gray-900'>
                {booking.nurse_given_name}
              </div>
            </div>
          </div>

          <div className='text-sm text-gray-600'>
            {booking.nurse_location_address}
          </div>
        </div>

        {}
        <div className='mb-6'>
          <h3 className='text-lg font-semibold text-gray-900 mb-4'>
            Select reason for cancellation:
          </h3>
          {renderCancellationReasonsContent()}
        </div>

        {}
        <div className='flex justify-center w-full'>
          <button
            onClick={handleConfirmCancellation}
            disabled={
              !selectedReason ||
              isSubmitting ||
              reasonsError !== undefined ||
              cancellationReasons?.length === 0
            }
            className='w-full md:w-1/3 bg-nursery-blue text-white py-3 px-4 rounded-lg font-semibold hover:bg-nursery-darkBlue disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors'
          >
            {isSubmitting
              ? 'Confirming Cancellation...'
              : 'Confirm Cancellation'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CancelBooking;
