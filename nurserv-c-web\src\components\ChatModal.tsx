import React from 'react';
import { ChatInterface, ChatState } from './ChatInterface';
import { Chat<PERSON><PERSON><PERSON> } from './ChatRenderer';

interface ChatModalProps {
  selectedNurse?: {
    nurse_cognitoId: string;
    nurse_given_name: string;
  } | null;
  isOpen?: boolean;
  onClose?: () => void;

  mode?: 'modal' | 'fullscreen';
}

const ChatModal: React.FC<ChatModalProps> = ({
  selectedNurse,
  isOpen = true,
  onClose,
  mode = 'modal',
}) => {
  return (
    <ChatInterface
      selectedNurse={selectedNurse}
      isOpen={isOpen}
      onClose={onClose}
      mode={mode}
    >
      {(chatState: ChatState) => (
        <ChatRenderer chatState={chatState} mode={mode} isOpen={isOpen} />
      )}
    </ChatInterface>
  );
};

export default ChatModal;
