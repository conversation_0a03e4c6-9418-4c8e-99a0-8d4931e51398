import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>O<PERSON>, Trash2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  showSuccessToast,
  showErrorToast,
  COMMON_SUCCESS_MESSAGES,
} from '@/utils/toast';
import { useDeleteAccountMutation } from '@/store/api/apiSlice';
import bg from '../../public/Images/bg4.png';

const PHONE_PREFIX = '+91';
const MIN_PASSWORD_LENGTH = 6;
const MAX_PHONE_LENGTH = 13;
const SUCCESS_DELAY = 2000;
const TOAST_DURATION = 3000;
const PHONE_REGEX = /^\+91\d{10}$/;

const ERROR_MESSAGES = {
  PHONE_REQUIRED: 'Phone number is required',
  PHONE_INVALID: 'Please enter a valid 10-digit phone number',
  PASSWORD_FIELD_REQUIRED: 'Password is required',
  PASSWORD_MIN_LENGTH: `Password must be at least ${MIN_PASSWORD_LENGTH} characters`,
  AUTHENTICATION_FAILED:
    'Invalid credentials. Please check your phone number and password.',
  DELETE_FAILED: 'Failed to delete account. Please try again.',
} as const;

interface FormErrors {
  phone_number?: string;
  password?: string;
}

interface DeleteAccountError {
  status?: number;
  data?: {
    message?: string;
  };
  message?: string;
}

const validatePhoneNumber = (phone: string): boolean => {
  const cleanPhone = phone.replace(/\s+/g, '');
  return PHONE_REGEX.test(cleanPhone);
};

const validatePassword = (password: string): boolean => {
  return password.length >= MIN_PASSWORD_LENGTH;
};

const DeleteAccount: React.FC = () => {
  const [phoneNumber, setPhoneNumber] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [errors, setErrors] = useState<FormErrors>({});
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [showConfirmModal, setShowConfirmModal] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [deleteData, { isLoading }] = useDeleteAccountMutation();
  const navigate = useNavigate();

  const handlePhoneNumberChange = (value: string): void => {
    if (!value.startsWith(PHONE_PREFIX)) {
      value = PHONE_PREFIX + value.replace(/^\+?91?/, '');
    }

    const cleanValue = PHONE_PREFIX + value.slice(3).replace(/\D/g, '');

    if (cleanValue.length <= MAX_PHONE_LENGTH) {
      setPhoneNumber(cleanValue);
    }

    if (errors.phone_number) {
      setErrors(prev => ({ ...prev, phone_number: undefined }));
    }
  };

  const handleInputChange = (field: keyof FormErrors, value: string): void => {
    switch (field) {
      case 'phone_number':
        handlePhoneNumberChange(value);
        break;
      case 'password':
        setPassword(value);
        break;
      default:
        break;
    }
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!phoneNumber.trim()) {
      newErrors.phone_number = ERROR_MESSAGES.PHONE_REQUIRED;
    } else if (!validatePhoneNumber(phoneNumber)) {
      newErrors.phone_number = ERROR_MESSAGES.PHONE_INVALID;
    }

    if (!password) {
      newErrors.password = ERROR_MESSAGES.PASSWORD_FIELD_REQUIRED;
    } else if (!validatePassword(password)) {
      newErrors.password = ERROR_MESSAGES.PASSWORD_MIN_LENGTH;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleDeleteClick = (): void => {
    if (validateForm()) {
      setShowConfirmModal(true);
    }
  };

  const handleConfirmDelete = async (): Promise<void> => {
    setIsDeleting(true);
    try {
      await deleteData({
        phone_number: phoneNumber,
        password,
      }).unwrap();

      setTimeout(() => {
        setIsDeleting(false);
        setShowConfirmModal(false);
        showSuccessToast(COMMON_SUCCESS_MESSAGES.ACCOUNT_DELETED, {
          duration: SUCCESS_DELAY,
        });
        setPhoneNumber('');
        setPassword('');
        setErrors({});

        navigate('/');
      }, SUCCESS_DELAY);
    } catch (error) {
      const typedError = error as DeleteAccountError;
      setIsDeleting(false);
      setShowConfirmModal(false);

      if (typedError?.data?.message) {
        showErrorToast(typedError.data.message, {
          duration: TOAST_DURATION,
        });
      } else if (typedError?.message) {
        showErrorToast(typedError.message, {
          duration: TOAST_DURATION,
        });
      } else {
        showErrorToast(ERROR_MESSAGES.DELETE_FAILED, {
          duration: TOAST_DURATION,
        });
      }

      if (typedError?.status === 401 || typedError?.status === 403) {
        setErrors({
          password: ERROR_MESSAGES.AUTHENTICATION_FAILED,
        });
      }
    }
  };

  const handleCancelModal = (): void => {
    if (!isDeleting) {
      setShowConfirmModal(false);
    }
  };

  const handlePhoneFocus = (e: React.FocusEvent<HTMLInputElement>): void => {
    if (!phoneNumber.startsWith(PHONE_PREFIX)) {
      setPhoneNumber(PHONE_PREFIX + phoneNumber);
    }

    setTimeout(() => {
      const newLength = phoneNumber.startsWith(PHONE_PREFIX)
        ? phoneNumber.length
        : phoneNumber.length + 3;
      e.target.setSelectionRange(newLength, newLength);
    }, 0);
  };

  const handlePhoneBlur = (): void => {
    if (phoneNumber === PHONE_PREFIX) {
      setPhoneNumber('');
    }
  };

  const handlePhoneKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>
  ): void => {
    const target = e.target as HTMLInputElement;
    const cursorPosition = target.selectionStart || 0;
    if ((e.key === 'Backspace' || e.key === 'Delete') && cursorPosition <= 3) {
      e.preventDefault();
    }
  };

  return (
    <div className='min-h-screen bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button
            onClick={() => navigate(-1)}
            className='mr-3'
            disabled={isDeleting}
            aria-label='Go back'
          >
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Delete Account</h1>
        </div>
      </header>

      {}
      <div className='max-w-md mx-auto p-6 md:mt-8 mt-2'>
        <div className='bg-[#F2F2F2] rounded-2xl shadow-xl border border-red-100'>
          {}
          <div className='bg-red-50 p-6 rounded-t-2xl border-b border-red-100'>
            <div className='flex items-center space-x-3 text-red-700'>
              <AlertTriangle className='h-8 w-8 flex-shrink-0' />
              <div>
                <h2 className='text-lg font-semibold'>Warning</h2>
                <p className='text-sm text-red-600 mt-1'>
                  This action cannot be undone. All your data will be
                  permanently deleted.
                </p>
              </div>
            </div>
          </div>

          {}
          <div className='p-6 space-y-6'>
            <p className='text-gray-600 text-sm'>
              Please confirm your identity to proceed with account deletion.
            </p>

            {}
            <div className='space-y-2'>
              <label
                htmlFor='phoneNumber'
                className='block text-sm font-medium text-gray-600'
              >
                Phone Number *
              </label>
              <input
                id='phoneNumber'
                type='tel'
                value={phoneNumber}
                onChange={e =>
                  handleInputChange('phone_number', e.target.value)
                }
                onFocus={handlePhoneFocus}
                onBlur={handlePhoneBlur}
                onKeyDown={handlePhoneKeyDown}
                disabled={isDeleting}
                className={`w-full px-4 py-2 bg-white border-2 border-gray-300 rounded-lg transition-colors focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed ${
                  errors.phone_number
                    ? 'border-red-300 focus:border-red-400'
                    : 'border-gray-200 focus:ring-1 focus:border-nursery-blue bg-white'
                }`}
                placeholder='Enter your phone number'
                autoComplete='tel'
                aria-describedby={
                  errors.phone_number ? 'phone-error' : undefined
                }
              />
              {errors.phone_number && (
                <p
                  id='phone-error'
                  className='text-red-500 text-sm flex items-center space-x-1'
                  role='alert'
                >
                  <AlertTriangle className='h-4 w-4' />
                  <span>{errors.phone_number}</span>
                </p>
              )}
            </div>

            {}
            <div className='space-y-2'>
              <label
                htmlFor='password'
                className='block text-sm font-medium text-gray-600'
              >
                Current Password *
              </label>
              <div className='relative'>
                <input
                  id='password'
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={e => handleInputChange('password', e.target.value)}
                  disabled={isDeleting}
                  className={`w-full px-4 py-2 pr-12 bg-white border-2 border-gray-300 rounded-lg transition-colors focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed ${
                    errors.password
                      ? 'border-red-300 focus:border-red-500'
                      : 'border-gray-200 focus:ring-1 focus:border-nursery-blue bg-white'
                  }`}
                  placeholder='Enter your current password'
                  autoComplete='current-password'
                  aria-describedby={
                    errors.password ? 'password-error' : undefined
                  }
                />
                <button
                  type='button'
                  onClick={() => setShowPassword(!showPassword)}
                  disabled={isDeleting}
                  className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed'
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? (
                    <Eye className='h-5 w-5' />
                  ) : (
                    <EyeOff className='h-5 w-5' />
                  )}
                </button>
              </div>
              {errors.password && (
                <p
                  id='password-error'
                  className='text-red-500 text-sm flex items-center space-x-1'
                  role='alert'
                >
                  <AlertTriangle className='h-4 w-4' />
                  <span>{errors.password}</span>
                </p>
              )}
            </div>

            {}
            <button
              onClick={handleDeleteClick}
              disabled={isDeleting || isLoading}
              className='w-full bg-nursery-blue hover:bg-red-500 text-white font-semibold py-2 px-6 rounded-lg transition-colors duration-200 transform focus:outline-none focus:ring-3 focus:ring-red-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed'
            >
              <Trash2 className='h-5 w-5' />
              <span>Delete My Account</span>
            </button>

            <p className='text-center text-xs text-gray-500'>
              By clicking &quot;Delete My Account&quot;, you acknowledge that
              this action is permanent and irreversible.
            </p>
          </div>
        </div>
      </div>

      {}
      {showConfirmModal && (
        <div className='fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50'>
          <div className='bg-white rounded-2xl shadow-2xl max-w-md w-full'>
            <div className='p-6'>
              <div className='flex items-center space-x-3 mb-4'>
                <div className='bg-red-100 p-3 rounded-full'>
                  <AlertTriangle className='h-8 w-8 text-red-600' />
                </div>
                <div>
                  <h3 className='text-lg font-bold text-gray-900'>
                    Confirm Account Deletion
                  </h3>
                  <p className='text-sm text-gray-600'>
                    This action cannot be undone
                  </p>
                </div>
              </div>

              <div className='bg-red-50 p-4 rounded-lg mb-6'>
                <p className='text-red-800 text-sm font-medium'>
                  Are you absolutely sure you want to delete your account?
                </p>
                <ul className='text-red-700 text-sm mt-2 space-y-1'>
                  <li>• All your personal data will be permanently deleted</li>
                  <li>• You will lose access to all services</li>
                  <li>• This action cannot be reversed</li>
                </ul>
              </div>

              <div className='flex space-x-3'>
                <button
                  onClick={handleCancelModal}
                  disabled={isDeleting}
                  className='flex-1 px-4 py-2 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium  disabled:cursor-not-allowed'
                >
                  Cancel
                </button>
                <button
                  onClick={handleConfirmDelete}
                  disabled={isDeleting}
                  className='flex-1 bg-nursery-blue hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors duration-200 font-medium  flex items-center justify-center space-x-2 disabled:cursor-not-allowed'
                >
                  {isDeleting ? (
                    <>
                      <div className='animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent'></div>
                      <span>Deleting...</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className='h-4 w-4' />
                      <span>Yes, Delete</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DeleteAccount;
