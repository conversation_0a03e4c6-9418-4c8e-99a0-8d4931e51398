import React from 'react';

interface PulseLoaderProps {
  color?: 'nursery';
  message?: string;
  className?: string;
}

const PulseLoader: React.FC<PulseLoaderProps> = ({
  color = 'nursery',
  message = 'Loading...',
  className = '',
}) => {
  const colorClasses = {
    nursery: 'text-nursery-darkBlue stroke-nursery-darkBlue',
  };

  const size = {
    container: 'h-24 w-24',
    icon: 'h-14 w-32',
    text: 'text-md',
    strokeWidth: 3,
  };

  const animationStyle = {
    animation: 'ecgAnimation 2s linear infinite',
    strokeDasharray: 1000,
    strokeDashoffset: 1000,
  };

  return (
    <div className={`flex flex-col items-center  ${className}`}>
      {}
      <style>
        {`
          @keyframes ecgAnimation {
            0% { stroke-dashoffset: 1000; }
            100% { stroke-dashoffset: 0; }
          }
        `}
      </style>

      <div
        className={`relative ${size.container} flex items-center justify-center`}
      >
        <svg
          viewBox='0 0 120 40'
          className={`${size.icon} ${colorClasses[color]}`}
        >
          <path
            fill='none'
            strokeLinecap='round'
            strokeLinejoin='round'
            strokeWidth={size.strokeWidth}
            style={animationStyle}
            d='M0,20 L10,20 C12,20 15,10 15,10 C15,10 18,30 20,30 C22,30 25,10 25,10 C25,10 28,35 30,35 C32,35 35,5 35,5 C35,5 38,25 40,25 C42,25 45,15 45,15 C45,15 48,25 50,25 C52,25 55,10 55,10 C55,10 58,40 60,40 C62,40 65,0 65,0 C65,0 68,20 70,20 C72,20 75,10 75,10 C75,10 78,20 80,20 C80,20 120,20 120,20'
          />
        </svg>
      </div>

      {message && (
        <p
          className={`mt-0 ${size.text} font-medium ${colorClasses[color].split(' ')[0]}`}
        >
          {message}
        </p>
      )}
    </div>
  );
};

const ResponsiveLoader = () => {
  return (
    <div className='flex items-center justify-center min-h-screen'>
      <PulseLoader color='nursery' message='Loading...' />
    </div>
  );
};

export default ResponsiveLoader;
