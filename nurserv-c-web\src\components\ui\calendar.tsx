import * as React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { DayPicker } from 'react-day-picker';
import { cn } from '@/lib/utils';
import { buttonVariants } from '@/components/ui/button';

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  const today = new Date();

  const defaultDate = new Date(today.getFullYear() - 18, today.getMonth());
  const [year, setYear] = React.useState(defaultDate.getFullYear());
  const [month, setMonth] = React.useState(defaultDate.getMonth());

  const years = Array.from(
    { length: 82 },
    (_, i) => today.getFullYear() - 18 - i
  );
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  return (
    <div className='bg-gray-200 shadow-lg rounded-lg'>
      <div className='relative grid grid-cols-3 gap-4 mb-2'>
        <select
          value={year}
          onChange={e => setYear(Number(e.target.value))}
          className='relative z-50 border rounded-md shadow-lg h-fit p-1'
        >
          {years.map(y => (
            <option key={y} value={y}>
              {y}
            </option>
          ))}
        </select>
        <select
          value={month}
          onChange={e => setMonth(Number(e.target.value))}
          className='relative z-50 border rounded-md shadow-lg max-h-10 p-1'
        >
          {months.map((m, index) => (
            <option key={index} value={index}>
              {m}
            </option>
          ))}
        </select>
      </div>
      <DayPicker
        showOutsideDays={showOutsideDays}
        month={new Date(year, month)}
        className={cn('p-1', className)}
        classNames={{
          months:
            'flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0',
          month: 'space-y-4',
          caption: 'hidden',
          table: 'w-full border-collapse space-y-1',
          head_row: 'flex',
          head_cell:
            'text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]',
          row: 'flex w-full mt-2',
          cell: 'h-9 w-9 text-center text-sm p-0 relative focus-within:relative focus-within:z-20',
          day: cn(
            buttonVariants({ variant: 'ghost' }),
            'h-9 w-9 p-0 font-normal aria-selected:opacity-100'
          ),
          day_selected:
            'bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground',
          day_today: 'bg-accent text-accent-foreground',
          day_outside: 'text-muted-foreground opacity-50',
          day_disabled: 'hidden',
          ...classNames,
        }}
        components={{
          IconLeft: () => <ChevronLeft className='h-4 w-4' />,
          IconRight: () => <ChevronRight className='h-4 w-4' />,
        }}
        {...props}
      />
    </div>
  );
}

Calendar.displayName = 'Calendar';
export { Calendar };
