import { useState, useRef, useEffect, useCallback } from 'react';
import { ApiResponse, Address } from '@/store/api/apiSlice';

export const useAddressDropdown = (
  addressesResponse: ApiResponse<Address[]> | null
) => {
  const [showAddressDropdown, setShowAddressDropdown] = useState(false);
  const [focusedAddressIndex, setFocusedAddressIndex] = useState<number>(-1);
  const onOpenRef = useRef<HTMLDivElement>(null);
  const addressButtonRefs = useRef<(HTMLButtonElement | null)[]>([]);

  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (onOpenRef.current && !onOpenRef.current.contains(e.target as Node)) {
        setShowAddressDropdown(false);
      }
    };

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && showAddressDropdown) {
        setShowAddressDropdown(false);
      }
    };

    if (showAddressDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
    }
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [showAddressDropdown]);

  useEffect(() => {
    if (!showAddressDropdown) {
      setFocusedAddressIndex(-1);
    }
  }, [showAddressDropdown]);

  const handleDropdownKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (!showAddressDropdown || !addressesResponse?.data) return;

      const addresses = addressesResponse.data;

      if (e.key === 'Escape') {
        setShowAddressDropdown(false);
        setFocusedAddressIndex(-1);
      } else if (e.key === 'ArrowDown') {
        e.preventDefault();
        const nextIndex =
          focusedAddressIndex < addresses.length - 1
            ? focusedAddressIndex + 1
            : 0;
        setFocusedAddressIndex(nextIndex);
        addressButtonRefs.current[nextIndex]?.focus();
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        const prevIndex =
          focusedAddressIndex > 0
            ? focusedAddressIndex - 1
            : addresses.length - 1;
        setFocusedAddressIndex(prevIndex);
        addressButtonRefs.current[prevIndex]?.focus();
      }
    },
    [showAddressDropdown, addressesResponse?.data, focusedAddressIndex]
  );

  const toggleDropdown = () => {
    setShowAddressDropdown(!showAddressDropdown);
  };

  const closeDropdown = () => {
    setShowAddressDropdown(false);
  };

  return {
    showAddressDropdown,
    setShowAddressDropdown,
    focusedAddressIndex,
    setFocusedAddressIndex,
    onOpenRef,
    addressButtonRefs,
    handleDropdownKeyDown,
    toggleDropdown,
    closeDropdown,
  };
};
