import { useState, useEffect, useCallback } from 'react';
import type { Address } from '@/store/api/apiSlice';

interface AddressState {
  selectedAddress: Address | null;
  selectedAddressId: string | null;
  isInitialized: boolean;
}

export const useAddressSelection = (addresses: Address[] | undefined) => {
  const [addressState, setAddressState] = useState<AddressState>({
    selectedAddress: null,
    selectedAddressId: null,
    isInitialized: false,
  });

  const STORAGE_KEY = 'selected_address_session';

  const saveAddressToSession = useCallback((address: Address) => {
    try {
      const addressData = {
        id: address.id,
        name: address.name,
        address: address.address,
        icon: address.icon,
        latitude: address.latitude,
        longitude: address.longitude,
        created_at: address.created_at,
        updated_at: address.updated_at,
        timestamp: Date.now(),
      };
      sessionStorage.setItem(STORAGE_KEY, JSON.stringify(addressData));
    } catch (error) {
      console.warn('Failed to save address to session storage:', error);
    }
  }, []);

  const loadAddressFromSession = useCallback((): Address | null => {
    try {
      const stored = sessionStorage.getItem(STORAGE_KEY);
      if (stored) {
        const addressData = JSON.parse(stored);

        if (Date.now() - addressData.timestamp < 3600000) {
          return {
            id: addressData.id,
            name: addressData.name,
            address: addressData.address,
            icon: addressData.icon,
            latitude: addressData.latitude,
            longitude: addressData.longitude,
            created_at: addressData.created_at ?? null,
            updated_at: addressData.updated_at ?? null,
          };
        } else {
          sessionStorage.removeItem(STORAGE_KEY);
        }
      }
    } catch (error) {
      console.warn('Failed to load address from session storage:', error);
    }
    return null;
  }, []);

  const clearAddressSession = useCallback(() => {
    try {
      sessionStorage.removeItem(STORAGE_KEY);
    } catch (error) {
      console.warn('Failed to clear address session:', error);
    }
  }, []);

  useEffect(() => {
    if (!addresses || addresses.length === 0 || addressState.isInitialized) {
      return;
    }

    const storedAddress = loadAddressFromSession();

    if (storedAddress) {
      const existingAddress = addresses.find(
        addr => addr.id === storedAddress.id
      );
      if (existingAddress) {
        setAddressState({
          selectedAddress: existingAddress,
          selectedAddressId: String(existingAddress.id),
          isInitialized: true,
        });
        return;
      }
    }

    let defaultAddress: Address;

    if (addresses.length === 1) {
      defaultAddress = addresses[0];
    } else {
      const homeAddress = addresses.find(addr => addr.icon === 'home');
      defaultAddress = homeAddress || addresses[0];
    }

    setAddressState({
      selectedAddress: defaultAddress,
      selectedAddressId: String(defaultAddress.id),
      isInitialized: true,
    });

    saveAddressToSession(defaultAddress);
  }, [
    addresses,
    addressState.isInitialized,
    loadAddressFromSession,
    saveAddressToSession,
  ]);

  const selectAddress = useCallback(
    (address: Address) => {
      setAddressState({
        selectedAddress: address,
        selectedAddressId: String(address.id),
        isInitialized: true,
      });

      saveAddressToSession(address);
    },
    [saveAddressToSession]
  );

  const resetAddressSelection = useCallback(() => {
    setAddressState({
      selectedAddress: null,
      selectedAddressId: null,
      isInitialized: false,
    });
    clearAddressSession();
  }, [clearAddressSession]);

  return {
    selectedAddress: addressState.selectedAddress,
    selectedAddressId: addressState.selectedAddressId,
    isInitialized: addressState.isInitialized,
    selectAddress,
    resetAddressSelection,
  };
};
