import { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  useGetProfileQuery,
  useGetAddressesQuery,
  useGetProfileDetailsQuery,
  useGetBookingsByCustomerQuery,
} from '@/store/api/apiSlice';
import { showErrorToast } from '@/utils/toast';

export const useHomeData = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const username = location.state?.username;
  const given_name = location.state?.given_name;
  const address = location.state?.address;
  const customer_set_location = location.state?.customer_set_location;
  const user_id = location.state?.user_id;

  const {
    data: profile,
    isLoading: profileLoading,
    error: profileError,
  } = useGetProfileQuery(
    { username },
    {
      skip: !username,
    }
  );

  const { data: profileDetails, isLoading: profileDetailsLoading } =
    useGetProfileDetailsQuery(undefined, {
      skip: !username,
    });

  const {
    data: addressesResponse,
    isLoading: addressesLoading,
    error: addressesError,
    refetch: refetchAddresses,
  } = useGetAddressesQuery(undefined, {
    refetchOnMountOrArgChange: true,
    skip: !username,
  });

  const userId = profileDetails?.details?.cognito_id;
  const customerName = profileDetails?.details?.given_name;
  const customerId = user_id || profileDetails?.details?.cognito_id;

  useEffect(() => {
    if (userId) {
      localStorage.setItem('userId', userId);
    }
  }, [userId]);

  const {
    data: bookingResponse,
    isLoading: bookingLoading,
    error: bookingError,
  } = useGetBookingsByCustomerQuery(userId, {
    skip: !userId,
    pollingInterval: 60000,
    refetchOnFocus: true,
    refetchOnReconnect: true,
  });

  useEffect(() => {
    if (!username) {
      navigate('/login');
      return;
    }
    if (profileError) {
      showErrorToast('Failed to load profile');
      return;
    }

    if (!profileLoading && profile && !customer_set_location) {
      if (!profile.customer_set_location) {
        navigate('/location');
      }
    }
  }, [
    profile,
    profileLoading,
    profileError,
    username,
    navigate,
    customer_set_location,
  ]);

  return {
    username,
    given_name,
    address,
    customer_set_location,
    user_id,

    profile,
    profileLoading,
    profileError,
    profileDetails,
    profileDetailsLoading,

    userId,
    customerName,
    customerId,

    addressesResponse,
    addressesLoading,
    addressesError,
    refetchAddresses,

    bookingResponse,
    bookingLoading,
    bookingError,
  };
};
