import { useState, useEffect } from 'react';

export const useScreenSize = () => {
  const [mobileView, setMobileView] = useState(false);

  const checkScreenSize = () => {
    setMobileView(window.innerWidth < 535);
  };

  useEffect(() => {
    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);
    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  return { mobileView };
};
