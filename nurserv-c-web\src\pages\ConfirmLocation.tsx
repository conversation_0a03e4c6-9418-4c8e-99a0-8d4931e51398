import React, { useState } from 'react';
import { CheckCircle, MapPin } from 'lucide-react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { useCreateBookingMutation } from '@/store/api/apiSlice';
import { showErrorToast, handleApiError } from '@/utils/toast';

interface LocationConfirmationProps {
  title?: string;
  subtitle?: string;
  address?: string;
  onConfirm?: () => void;
}

const LocationConfirmation: React.FC<LocationConfirmationProps> = ({
  title: propTitle,
  address: propAddress,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [errorMessage, setErrorMessage] = useState('');

  const bookingDetails = location.state?.bookingDetails;
  const _nurse = location.state?.nurse;
  const customerAddress = bookingDetails?.customer_booked_location_address;
  const customerName = bookingDetails?.customer_given_name;

  const title = customerName ? customerName.toUpperCase() : propTitle || 'HOME';
  const address = customerAddress || propAddress;

  const [createBooking, { isLoading }] = useCreateBookingMutation();

  const handleCancelRequest = () => {
    navigate(-1);
  };

  const onConfirm = async () => {
    setErrorMessage('');

    if (!bookingDetails) {
      setErrorMessage('Booking details not found. Please try again.');
      return;
    }

    try {
      const response = await createBooking(bookingDetails).unwrap();

      navigate('/booking-success', {
        state: {
          booking: response,
          bookingDetails: bookingDetails,
        },
      });
    } catch (error: unknown) {
      console.error('Booking error:', error);

      const apiError = error as {
        data?: { error?: string };
        message?: string;
        status?: number;
      };
      const errorMessage = apiError?.data?.error || apiError?.message;
      const statusCode = apiError?.status;

      switch (statusCode) {
        case 403:
          if (errorMessage?.includes('Only customers can create bookings')) {
            showErrorToast(
              'Access denied. Only customers can create bookings.'
            );
          } else {
            showErrorToast('Access denied. Please check your permissions.');
          }
          break;
        case 404:
          showErrorToast(
            'Nurse not found. Please try selecting a different nurse.'
          );
          break;
        case 409:
          showErrorToast(
            'Nurse is not available for the selected date and time slot. Please choose a different slot.'
          );
          break;
        default:
          handleApiError(error, {
            [statusCode]: errorMessage || 'Booking failed. Please try again.',
          });
      }

      setErrorMessage(errorMessage || 'Booking failed. Please try again.');
    }
  };

  return (
    <div className='min-h-screen bg-white flex flex-col items-center justify-center p-4'>
      {}
      <div className='mb-8'>
        <div className='w-24 h-24 bg-green-100 rounded-full flex items-center justify-center'>
          <CheckCircle className='w-12 h-12 text-green-600' />
        </div>
      </div>

      {}
      <h1 className='text-2xl text-gray-800 font-medium mb-2 text-center'>
        Delivering service to
      </h1>

      {}
      <h2 className='text-xl font-bold text-nursery-darkBlue mb-2 text-center'>
        {title} <span className='text-gray-800'>at</span>
      </h2>

      {}
      <div className=' mb-6 max-w-md'>
        <p className='flex flex-row md:gap-2 gap-0  text-gray-700 text-center'>
          <MapPin className='w-5 h-5 text-gray-600' />
          {address}
        </p>
      </div>

      {}
      {errorMessage && (
        <div className='mb-4 p-3 bg-red-100 border border-red-300 rounded-md text-red-600 text-sm max-w-md w-full text-center'>
          {errorMessage}
        </div>
      )}

      {}
      <div className='flex flex-col space-y-4 w-full max-w-md'>
        <Button
          variant='outline'
          onClick={handleCancelRequest}
          disabled={isLoading}
          className='w-full py-3 text-gray-700 bg-gray-200 border-gray-300 hover:bg-gray-300 shadow-lg disabled:opacity-50'
        >
          Cancel Request
        </Button>
        <Button
          onClick={onConfirm}
          disabled={isLoading}
          className='w-full py-3 bg-nursery-darkBlue hover:bg-nursery-blue text-white disabled:opacity-50'
        >
          {isLoading ? 'Creating Booking...' : 'Confirm Location'}
        </Button>
      </div>
    </div>
  );
};

export default LocationConfirmation;
