import { useLocation, useNavigate } from 'react-router-dom';
import { useEffect } from 'react';
import Logo from '../components/Logo';

const NotFound = () => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    console.error(
      '404 Error: User attempted to access non-existent route:',
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className='min-h-screen flex md:flex-row flex-col items-center justify-center bg-nursery-darkBlue p-4'>
      <div className='md:pr-6 p-6 flex justify-center items-center'>
        <img
          src='../../public/Images/pnf404.svg'
          className='md:w-[390px] w-[310px] object-cover'
          alt='404 Page Not Found...'
        />
      </div>
      <div className='text-center md:pl-6 mt-10 md:mt-0'>
        <Logo />
        <h1 className='text-4xl font-bold mb-4 text-white'>404</h1>
        <p className='text-xl text-white mb-4'>Oops! Page not found</p>
        <p
          onClick={() => navigate(-1)}
          className=' cursor-pointer text-lg text-white hover:text-xl transition-all duration-300 underline'
        >
          Return to Previous Page
        </p>
      </div>
    </div>
  );
};

export default NotFound;
