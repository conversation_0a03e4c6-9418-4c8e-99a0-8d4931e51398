import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { z } from 'zod';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import {
  showSuccessToast,
  showErrorToast,
  handleApiError,
  COMMON_SUCCESS_MESSAGES,
} from '@/utils/toast';
import { useVerifyOTPMutation } from '@/store/api/apiSlice';
import { useResendOTPMutation } from '@/store/api/apiSlice';

const otpSchema = z.object({
  confirmationCode: z
    .string()
    .length(6, 'OTP must be 6 digits')
    .regex(/^\d+$/, 'OTP must contain only numbers'),
});

const OTPVerification = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [verifyOTP, { isLoading }] = useVerifyOTPMutation();
  const [resendOtp, { isLoading: isResending }] = useResendOTPMutation();
  const [confirmationCode, setOtp] = useState('');
  const [timer, setTimer] = useState(30);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const username = location.state?.username;
  const phone_number = location.state?.phone_number;

  useEffect(() => {
    startTimer();
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  if (!username) {
    navigate('/signup');
    return null;
  }

  const startTimer = () => {
    setTimer(30);
    if (timerRef.current) clearInterval(timerRef.current);

    timerRef.current = setInterval(() => {
      setTimer(prevTimer => {
        if (prevTimer <= 1) {
          clearInterval(timerRef.current!);
          return 0;
        }
        return prevTimer - 1;
      });
    }, 1000);
  };

  const handleChange = (value: string) => {
    setErrors(prev => ({ ...prev, otp: '' }));
    setOtp(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validationResult = otpSchema.safeParse({ confirmationCode });

    if (!validationResult.success) {
      const fieldErrors: Record<string, string> = {};
      validationResult.error.errors.forEach(err => {
        fieldErrors[err.path[0]] = err.message;
      });

      setErrors(fieldErrors);
      return;
    }

    try {
      const result = await verifyOTP({ username, confirmationCode }).unwrap();

      if (result.message === 'User confirmed successfully') {
        showSuccessToast(COMMON_SUCCESS_MESSAGES.OTP_VERIFIED);
        navigate('/login');
      } else {
        showErrorToast(result.message || 'OTP verification failed');
      }
    } catch (error: unknown) {
      console.error('OTP verification error:', error);

      const apiError = error as {
        data?: { error?: string };
        message?: string;
        status?: number;
      };
      const errorMessage = apiError?.data?.error || apiError?.message;
      const statusCode = apiError?.status;

      switch (statusCode) {
        case 400:
          if (errorMessage?.includes('Invalid confirmation code')) {
            showErrorToast(
              'Invalid confirmation code. Please check and try again.'
            );
          } else if (errorMessage?.includes('expired')) {
            showErrorToast(
              'Confirmation code has expired. Please request a new OTP.'
            );
          } else if (errorMessage?.includes('Missing required fields')) {
            showErrorToast('Missing required information. Please try again.');
          } else {
            showErrorToast(errorMessage || 'Invalid confirmation code');
          }
          break;
        default:
          handleApiError(error, {
            [statusCode]: 'OTP verification failed. Please try again.',
          });
      }
    }
  };

  const handleResend = async (e: React.FormEvent) => {
    e.preventDefault();
    if (timer > 0) return;
    try {
      const response = await resendOtp({ phone_number }).unwrap();
      if (response) {
        showSuccessToast('New OTP has been sent to your mobile number');
        startTimer();
      }
    } catch (error: unknown) {
      console.error('Resend OTP error:', error);

      const apiError = error as {
        data?: { error?: string };
        message?: string;
        status?: number;
      };
      const errorMessage = apiError?.data?.error || apiError?.message;
      const statusCode = apiError?.status;

      switch (statusCode) {
        case 400:
          if (errorMessage?.includes('Phone number is required')) {
            showErrorToast('Phone number is required. Please try again.');
          } else if (errorMessage?.includes('Invalid phone number format')) {
            showErrorToast(
              'Invalid phone number format. Please check and try again.'
            );
          } else {
            showErrorToast(errorMessage || 'Invalid request');
          }
          break;
        case 404:
          showErrorToast('User not found. Please check your phone number.');
          break;
        case 429:
          showErrorToast('Too many attempts. Please try again later.');
          break;
        default:
          handleApiError(error, {
            [statusCode]: 'Failed to resend OTP. Please try again.',
          });
      }
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins < 10 ? '0' : ''}${mins}:${secs < 10 ? '0' : ''}${secs}`;
  };

  return (
    <div className='relative h-screen w-full overflow-hidden'>
      {}
      <div className='absolute inset-0 w-full h-full z-0'>
        <img
          src='../../public/Images/bg4.png'
          alt='Background Wallpaper'
          className='w-full h-full object-cover'
        />
      </div>

      {}
      <div className='absolute inset-0 bg-black bg-opacity-30' />
      <div className=' relative z-20 flex-1 flex flex-col items-center justify-center px-5 md:pt-32 pt-20'>
        <Link to='/'>
          <img
            src='../../public/Images/Logo.svg'
            alt='Nurses team'
            className='w-[200px] h-[60px] object-cover bg-transparent animate-fade-in max-w-md mx-auto mb-4 '
          />
        </Link>

        <Card className='w-full max-w-md bg-white rounded-md p-6 shadow-lg'>
          <h2 className='text-2xl font-bold text-center mb-6 text-nursery-blue'>
            Verify OTP
          </h2>
          <p className='text-center text-gray-800 mb-6'>
            Please enter the 6-digit OTP sent to your mobile number.
          </p>

          <form onSubmit={handleSubmit} className='space-y-4'>
            <div>
              <Input
                id='otp'
                type='text'
                placeholder='Enter 6-digit OTP'
                value={confirmationCode}
                onChange={e => handleChange(e.target.value)}
                className='w-full h-11 text-center text-base tracking-widest transition-all'
                maxLength={6}
              />
              {errors.confirmationCode && (
                <p className='text-red-500 text-sm text-center'>
                  {errors.confirmationCode}
                </p>
              )}
            </div>

            <Button
              type='submit'
              className='w-full h-10 bg-[#4AB4CE] text-white'
              disabled={
                isLoading || isResending || confirmationCode.length !== 6
              }
            >
              {isLoading ? 'Verifying...' : 'Verify OTP'}
            </Button>

            <Button
              type='button'
              variant='outline'
              onClick={() => navigate('/signup')}
              className='w-full h-10 border-[#4AB4CE] text-[#4AB4CE]'
            >
              Back to Sign Up
            </Button>
          </form>
          <div className='mt-6 text-center'>
            <button
              onClick={handleResend}
              disabled={timer > 0}
              className={`text-base ${timer > 0 ? 'text-gray-500' : 'text-nursery-blue font-medium'}`}
            >
              {timer > 0
                ? `Resend verification code after ${formatTime(timer)}`
                : 'Resend verification code'}
            </button>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default OTPVerification;
