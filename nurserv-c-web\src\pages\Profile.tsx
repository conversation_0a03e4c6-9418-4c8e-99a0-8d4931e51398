import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  User,
  Building2,
  Settings,
  Shield,
  ChevronRight,
  MapPinPlus,
} from 'lucide-react';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import bg from '../../public/Images/bg4.png';
import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import Footer from '@/components/Footer';
import { handleApiError } from '@/utils/toast';

const Profile = () => {
  const [_loading, _setLoading] = useState(true);
  const navigate = useNavigate();
  const { data: profile, error, isLoading } = useGetProfileDetailsQuery();

  useEffect(() => {
    if (error) {
      console.error('Profile details fetch error:', error);

      handleApiError(error, {
        401: 'Session expired. Please log in again.',
        403: 'Access denied. Please check your permissions.',
        404: 'Profile details not found. Please try refreshing.',
      });
    }
  }, [error]);

  if (isLoading) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='text-center'>
          <p className='text-gray-600'>
            Unable to load profile details. Please try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      <header className='relative w-full  overflow-hidden text-white p-3 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className=' object-cover w-full '
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />
        {}
        <div className='relative z-10 h-full w-full flex items-center mb-6'>
          <button onClick={() => navigate(-1)} className='mr-4'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>My Profile</h1>
        </div>

        {}
        <div className='relative flex items-center mb-4'>
          <div className='relative'>
            <Avatar className='h-16 w-16 bg-white text-nursery-blue'>
              <AvatarFallback className='bg-white text-nursery-blue text-xl'>
                <User className='h-10 w-10' />
              </AvatarFallback>
            </Avatar>
          </div>
          <div className='ml-4'>
            <h2 className='text-xl font-semibold'>
              {`${profile?.details?.given_name || 'Loading'} ${profile?.details?.family_name || 'Loading...'}`}{' '}
            </h2>
            <p className='text-sm'>
              {profile?.details?.phone_number || 'Loading Phone Number...'}
            </p>
            <p className='text-sm'>
              {profile?.details?.email || 'Loading Email...'}
            </p>
          </div>
        </div>
      </header>

      {}
      <main className='flex-1'>
        <div className='py-2'>
          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<User className='h-5 w-5 text-gray-500' />}
              label='Profile'
              onClick={() => navigate('/profile-details')}
            />
          </div>

          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<MapPinPlus className='h-5 w-5 text-gray-500' />}
              label='Manage Adresses'
              onClick={() => navigate('/address-manager')}
            />
          </div>

          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<Building2 className='h-5 w-5 text-gray-500' />}
              label='Bank Accounts'
              onClick={() => navigate('/bank-accounts')}
            />
          </div>

          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<Shield className='h-5 w-5 text-gray-500' />}
              label='Privacy Policy'
              onClick={() => navigate('/privacy-policy')}
            />
          </div>
          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<Settings className='h-5 w-5 text-gray-500' />}
              label='Settings'
              onClick={() => navigate('/settings')}
            />
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

const MenuItem = ({
  icon,
  label,
  onClick,
}: {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
}) => {
  return (
    <button
      onClick={onClick}
      className='flex items-center justify-between w-full px-4 py-4 border-b border-gray-100'
    >
      <div className='flex items-center'>
        <div className='mr-4'>{icon}</div>
        <span className='font-medium'>{label}</span>
      </div>
      <ChevronRight className='h-5 w-5 text-gray-400' />
    </button>
  );
};

export default Profile;
