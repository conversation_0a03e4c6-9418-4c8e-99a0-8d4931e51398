import React, { useEffect, useState } from 'react';
import ProfileHeader from '@/components/profile/ProfileHeader';
import PersonalDetailsSection from '@/components/profile/PersonalDetailsSection';
import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import { handleApiError } from '@/utils/toast';

const ProfileDetails = () => {
  const [personalExpanded, setPersonalExpanded] = useState(true);
  const [given_name, setGiven_name] = useState<string | ''>('');
  const [family_name, setFamily_name] = useState<string | ''>('');
  const [email, setEmail] = useState<string | ''>('');
  const { data, isLoading, error } = useGetProfileDetailsQuery();

  useEffect(() => {
    if (data) {
      setGiven_name(data?.details?.given_name || '');
      setFamily_name(data?.details?.family_name || '');
      setEmail(data?.details?.email || '');
    }
  }, [data]);

  useEffect(() => {
    if (error) {
      console.error('Profile details fetch error:', error);

      handleApiError(error, {
        401: 'Session expired. Please log in again.',
        403: 'Access denied. Please check your permissions.',
        404: 'Profile details not found. Please try refreshing.',
      });
    }
  }, [error]);

  if (isLoading) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen flex items-center justify-center'>
        <div className='text-center'>
          <p className='text-gray-600'>
            Unable to load profile details. Please try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen flex flex-col bg-white justify-center '>
      <ProfileHeader title='Profile Details' />
      {}
      <main className='flex-1 px-4 w-10/12 md:11/12  mx-auto transition-all duration-200 ease-linear '>
        <div className='mt-6'>
          <PersonalDetailsSection
            expanded={personalExpanded}
            setExpanded={setPersonalExpanded}
            given_name={given_name}
            setGiven_name={setGiven_name}
            family_name={family_name}
            setFamily_name={setFamily_name}
            email={email}
            setEmail={setEmail}
            isDisabled={true}
          />
        </div>
      </main>
    </div>
  );
};

export default ProfileDetails;
