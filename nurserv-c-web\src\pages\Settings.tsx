import { useNavigate } from 'react-router-dom';
import {
  <PERSON><PERSON>ef<PERSON>,
  ChevronRight,
  KeyRound,
  User,
  LogOut,
  X,
} from 'lucide-react';
import { useState } from 'react';
import { useGetProfileDetailsQuery } from '@/store/api/apiSlice';
import ResponsiveLoader from '@/components/Loader';
import bg from '../../public/Images/bg4.png';
import Footer from '@/components/Footer';
import { performLogout } from '@/utils/logout';

const Settings = () => {
  const [showLogoutModal, setShowLogoutModal] = useState(false);

  const handleLogoutClick = () => {
    setShowLogoutModal(true);
  };

  const handleLogoutConfirm = () => {
    performLogout();
    setShowLogoutModal(false);
    navigate('/login');
  };

  const handleLogoutCancel = () => {
    setShowLogoutModal(false);
  };

  const navigate = useNavigate();

  const {
    data: profile,
    isLoading: profileDetailsLoading,
    error: _profileDetailsError,
  } = useGetProfileDetailsQuery(undefined);

  const phone_number = profile?.details?.phone_number;

  if (profileDetailsLoading) {
    return (
      <div>
        <ResponsiveLoader />
      </div>
    );
  }

  return (
    <div className='min-h-screen flex flex-col bg-white'>
      {}
      <header className='relative w-full overflow-hidden text-white p-5 flex flex-col'>
        <div className='absolute inset-0 w-full h-full z-0 bg-fixed'>
          <img
            src={bg}
            alt='Background Wallpaper'
            className='object-cover w-full'
          />
        </div>
        <div className='absolute inset-0 w-full object-cover bg-black bg-opacity-20 z-0' />

        <div className='relative z-10 h-full min-w-full flex items-center md:mb-3 top-1 '>
          <button onClick={() => navigate(-1)} className='mr-3'>
            <ArrowLeft className='h-6 w-6' />
          </button>
          <h1 className='text-xl font-semibold'>Settings</h1>
        </div>
      </header>

      {}
      <main className='flex-1 '>
        <div className='py-2'>
          {}

          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<KeyRound className='h-5 w-5 text-gray-500' />}
              label='Password Manager'
              onClick={() =>
                navigate('/password-manager', { state: { phone_number } })
              }
            />
          </div>

          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <MenuItem
              icon={<User className='h-5 w-5 text-gray-500' />}
              label='Delete Account'
              onClick={() => navigate('/delete-account')}
            />
          </div>

          {}
          <div className='hover:shadow-lg transform-gpu transition-shadow duration-200 ease-in-out'>
            <button
              onClick={handleLogoutClick}
              className='flex items-center justify-between w-full px-4 py-4 border-b border-gray-100'
            >
              <div className='flex items-center'>
                <div className='mr-4'>
                  <LogOut className='h-5 w-5 text-red-500' />
                </div>
                <span className='font-medium text-red-500'>Logout</span>
              </div>
              <ChevronRight className='h-5 w-5 text-gray-500' />
            </button>
          </div>
        </div>

        {}
        {showLogoutModal && (
          <div className='fixed inset-0 bg-black bg-opacity-55 flex items-center justify-center z-50 p-4'>
            <div className='bg-white rounded-xl p-6 w-full max-w-sm mx-4 shadow-xl'>
              <div className='flex justify-between items-center mb-4'>
                <h3 className='text-lg font-semibold text-gray-900'>
                  Confirm Logout
                </h3>
                <button
                  onClick={handleLogoutCancel}
                  className='p-1 rounded-full transition-colors'
                >
                  <X className='h-5 w-5 text-gray-500 hover:text-red-600 hover:scale-110 transition-transform duration-200' />
                </button>
              </div>

              <p className='text-gray-700 mb-6'>
                Are you sure you want to logout?
              </p>

              <div className='flex gap-3'>
                <button
                  onClick={handleLogoutCancel}
                  className='flex-1 px-4 py-2 bg-[#F2F2F2] border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-300 hover:text-gray-800 transition-colors font-medium'
                >
                  Cancel
                </button>
                <button
                  onClick={handleLogoutConfirm}
                  className='flex-1 px-4 py-2 bg-nursery-blue text-white rounded-lg hover:bg-nursery-darkBlue transition-colors duration-200 font-medium shadow-lg'
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        )}
      </main>
      <Footer />
    </div>
  );
};

const MenuItem = ({
  icon,
  label,
  onClick,
}: {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
}) => {
  return (
    <button
      onClick={onClick}
      className='flex items-center justify-between w-full px-4 py-4 border-b border-gray-100'
    >
      <div className='flex items-center'>
        <div className='mr-4 '>{icon}</div>
        <span className='font-medium'>{label}</span>
      </div>
      <ChevronRight className='h-5 w-5 text-gray-500' />
    </button>
  );
};

export default Settings;
