import { configureStore } from '@reduxjs/toolkit';
import { apiSlice } from './api/apiSlice';
import { nurseApiSlice } from './api/nurseApiSlice';
import { chatApiSlice } from './api/chatApiSlice';

export const store = configureStore({
  reducer: {
    [apiSlice.reducerPath]: apiSlice.reducer,
    [nurseApiSlice.reducerPath]: nurseApiSlice.reducer,
    [chatApiSlice.reducerPath]: chatApiSlice.reducer,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware()
      .concat(apiSlice.middleware)
      .concat(nurseApiSlice.middleware)
      .concat(chatApiSlice.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
