
declare namespace google.maps {
  class Map {
    constructor(mapDiv: Element, opts?: MapOptions);
    fitBounds(bounds: LatLngBounds, padding?: number | Padding): void;
    getBounds(): LatLngBounds;
    getCenter(): LatLng;
    getClickableIcons(): boolean;
    getDiv(): Element;
    getHeading(): number;
    getMapTypeId(): MapTypeId;
    getProjection(): Projection;
    getStreetView(): StreetViewPanorama;
    getTilt(): number;
    getZoom(): number;
    panBy(x: number, y: number): void;
    panTo(latLng: LatLng | LatLngLiteral): void;
    panToBounds(latLngBounds: LatLngBounds | LatLngBoundsLiteral, padding?: number | Padding): void;
    setCenter(latlng: LatLng | LatLngLiteral): void;
    setClickableIcons(clickable: boolean): void;
    setHeading(heading: number): void;
    setMapTypeId(mapTypeId: MapTypeId | string): void;
    setOptions(options: MapOptions): void;
    setStreetView(panorama: StreetViewPanorama): void;
    setTilt(tilt: number): void;
    setZoom(zoom: number): void;
    controls: MVCArray<Node>[];
    data: Data;
    mapTypes: MapTypeRegistry;
    overlayMapTypes: MVCArray<MapType>;
  }

  interface MapOptions {
    backgroundColor?: string;
    center?: LatLng | LatLngLiteral;
    clickableIcons?: boolean;
    controlSize?: number;
    disableDefaultUI?: boolean;
    disableDoubleClickZoom?: boolean;
    draggable?: boolean;
    draggableCursor?: string;
    draggingCursor?: string;
    fullscreenControl?: boolean;
    fullscreenControlOptions?: FullscreenControlOptions;
    gestureHandling?: string;
    heading?: number;
    keyboardShortcuts?: boolean;
    mapId?: string;
    mapTypeControl?: boolean;
    mapTypeControlOptions?: MapTypeControlOptions;
    mapTypeId?: MapTypeId;
    maxZoom?: number;
    minZoom?: number;
    noClear?: boolean;
    panControl?: boolean;
    panControlOptions?: PanControlOptions;
    restriction?: MapRestriction;
    rotateControl?: boolean;
    rotateControlOptions?: RotateControlOptions;
    scaleControl?: boolean;
    scaleControlOptions?: ScaleControlOptions;
    scrollwheel?: boolean;
    streetView?: StreetViewPanorama;
    streetViewControl?: boolean;
    streetViewControlOptions?: StreetViewControlOptions;
    styles?: MapTypeStyle[];
    tilt?: number;
    zoom?: number;
    zoomControl?: boolean;
    zoomControlOptions?: ZoomControlOptions;
  }

  interface Padding {
    bottom: number;
    left: number;
    right: number;
    top: number;
  }

  interface LatLng {
    constructor(lat: number, lng: number, noWrap?: boolean): void;
    equals(other: LatLng): boolean;
    lat(): number;
    lng(): number;
    toString(): string;
    toJSON(): LatLngLiteral;
    toUrlValue(precision?: number): string;
  }

  interface LatLngLiteral {
    lat: number;
    lng: number;
  }

  interface LatLngBounds {
    constructor(sw?: LatLng | LatLngLiteral, ne?: LatLng | LatLngLiteral): void;
    contains(latLng: LatLng | LatLngLiteral): boolean;
    equals(other: LatLngBounds | LatLngBoundsLiteral): boolean;
    extend(point: LatLng | LatLngLiteral): LatLngBounds;
    getCenter(): LatLng;
    getNorthEast(): LatLng;
    getSouthWest(): LatLng;
    intersects(other: LatLngBounds | LatLngBoundsLiteral): boolean;
    isEmpty(): boolean;
    toJSON(): LatLngBoundsLiteral;
    toSpan(): LatLng;
    toString(): string;
    toUrlValue(precision?: number): string;
    union(other: LatLngBounds | LatLngBoundsLiteral): LatLngBounds;
  }

  interface LatLngBoundsLiteral {
    east: number;
    north: number;
    south: number;
    west: number;
  }

  namespace places {
    class Autocomplete extends MVCObject {
      constructor(inputField: HTMLInputElement, opts?: AutocompleteOptions);
      getBounds(): LatLngBounds;
      getPlace(): PlaceResult;
      setBounds(bounds: LatLngBounds | LatLngBoundsLiteral): void;
      setComponentRestrictions(restrictions: ComponentRestrictions): void;
      setFields(fields: string[]): void;
      setOptions(options: AutocompleteOptions): void;
      setTypes(types: string[]): void;
    }

    interface AutocompleteOptions {
      bounds?: LatLngBounds | LatLngBoundsLiteral;
      componentRestrictions?: ComponentRestrictions;
      fields?: string[];
      placeIdOnly?: boolean;
      strictBounds?: boolean;
      types?: string[];
    }

    interface ComponentRestrictions {
      country: string | string[];
    }

    interface PlaceResult {
      address_components?: AddressComponent[];
      formatted_address?: string;
      geometry?: PlaceGeometry;
      place_id?: string;
      name?: string;
      types?: string[];
      url?: string;
      utc_offset_minutes?: number;
    }

    interface AddressComponent {
      long_name: string;
      short_name: string;
      types: string[];
    }

    interface PlaceGeometry {
      location: LatLng;
      viewport: LatLngBounds;
    }
  }

  namespace event {
    function addListener(instance: any, eventName: string, handler: Function): MapsEventListener;
    function addListenerOnce(instance: any, eventName: string, handler: Function): MapsEventListener;
    function clearInstanceListeners(instance: any): void;
    function clearListeners(instance: any, eventName: string): void;
    function removeListener(listener: MapsEventListener): void;
    function trigger(instance: any, eventName: string, ...args: any[]): void;
  }

  interface MapsEventListener {
    remove(): void;
  }

  interface MVCObject {
    addListener(eventName: string, handler: Function): MapsEventListener;
    bindTo(key: string, target: MVCObject, targetKey?: string, noNotify?: boolean): void;
    changed(key: string): void;
    get(key: string): any;
    notify(key: string): void;
    set(key: string, value: any): void;
    setValues(values: any): void;
    unbind(key: string): void;
    unbindAll(): void;
  }

  interface MVCArray<T> extends MVCObject {
    clear(): void;
    forEach(callback: (elem: T, i: number) => void): void;
    getArray(): T[];
    getAt(i: number): T;
    getLength(): number;
    insertAt(i: number, elem: T): void;
    pop(): T;
    push(elem: T): number;
    removeAt(i: number): T;
    setAt(i: number, elem: T): void;
  }
}
