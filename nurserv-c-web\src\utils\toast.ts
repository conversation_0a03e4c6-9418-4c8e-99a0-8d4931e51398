import { toast as sonnerToast } from 'sonner';

const TOAST_STYLES = {
  error: {
    backgroundColor: '#FFFFFF',
    color: '#EF4444',
  },
  success: {
    backgroundColor: '#FFFFFF',
    color: '#16A34A',
  },
  info: {
    backgroundColor: '#FFFFFF',
    color: '#3B82F6',
  },
  warning: {
    backgroundColor: '#FFFFFF',
    color: '#F59E0B',
  },
} as const;

const TOAST_DURATIONS = {
  error: 3000,
  success: 2000,
  info: 3000,
  warning: 3000,
} as const;

interface ToastOptions {
  duration?: number;
  dismissible?: boolean;
  style?: Record<string, string>;
}

export const showErrorToast = (message: string, options: ToastOptions = {}) => {
  return sonnerToast.error(message, {
    style: options.style || TOAST_STYLES.error,
    duration: options.duration || TOAST_DURATIONS.error,
    dismissible: options.dismissible !== false,
    ...options,
  });
};

export const showSuccessToast = (
  message: string,
  options: ToastOptions = {}
) => {
  return sonnerToast.success(message, {
    style: options.style || TOAST_STYLES.success,
    duration: options.duration || TOAST_DURATIONS.success,
    dismissible: options.dismissible !== false,
    ...options,
  });
};

export const showInfoToast = (message: string, options: ToastOptions = {}) => {
  return sonnerToast.info(message, {
    style: options.style || TOAST_STYLES.info,
    duration: options.duration || TOAST_DURATIONS.info,
    dismissible: options.dismissible !== false,
    ...options,
  });
};

export const showWarningToast = (
  message: string,
  options: ToastOptions = {}
) => {
  return sonnerToast.warning(message, {
    style: options.style || TOAST_STYLES.warning,
    duration: options.duration || TOAST_DURATIONS.warning,
    dismissible: options.dismissible !== false,
    ...options,
  });
};

export const COMMON_ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  SERVER_ERROR: 'Server error. Please try again later.',
  AUTHENTICATION_FAILED:
    'Authentication failed. Please check your credentials.',
  INVALID_INPUT: 'Invalid input. Please check your details.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
} as const;

export const COMMON_SUCCESS_MESSAGES = {
  OTP_SENT: 'OTP has been sent to your mobile number.',
  OTP_VERIFIED: 'OTP verified successfully!',
  REGISTRATION_SUCCESS: 'Registration successful! Please verify your OTP.',
  PASSWORD_RESET_SUCCESS: 'Your password has been reset successfully.',
  ACCOUNT_DELETED: 'Your Account has been successfully Deleted.',
} as const;

interface ApiError {
  data?: {
    error?: string;
    message?: string;
  };
  message?: string;
  status?: number;
  name?: string;
}

export const handleApiError = (
  error: unknown,
  customMessages: Record<number, string> = {}
) => {
  const apiError = error as ApiError;
  const errorMessage =
    apiError?.data?.error || apiError?.data?.message || apiError?.message;
  const statusCode = apiError?.status;

  if (statusCode && customMessages[statusCode]) {
    showErrorToast(customMessages[statusCode]);
    return;
  }

  switch (statusCode) {
    case 400:
      showErrorToast(errorMessage || COMMON_ERROR_MESSAGES.INVALID_INPUT);
      break;
    case 401:
    case 403:
      showErrorToast(
        errorMessage || COMMON_ERROR_MESSAGES.AUTHENTICATION_FAILED
      );
      break;
    case 404:
      showErrorToast(errorMessage || 'Resource not found.');
      break;
    case 409:
      showErrorToast(errorMessage || 'Conflict occurred. Please try again.');
      break;
    case 500:
      showErrorToast(errorMessage || COMMON_ERROR_MESSAGES.SERVER_ERROR);
      break;
    default:
      if (apiError?.name === 'NetworkError' || !navigator.onLine) {
        showErrorToast(COMMON_ERROR_MESSAGES.NETWORK_ERROR);
      } else {
        showErrorToast(errorMessage || COMMON_ERROR_MESSAGES.GENERIC_ERROR);
      }
  }
};

export const toast = {
  error: showErrorToast,
  success: showSuccessToast,
  info: showInfoToast,
  warning: showWarningToast,
};
